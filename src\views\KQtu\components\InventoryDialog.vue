<template>
  <el-dialog
    title="物料盘点"
    :visible.sync="visible"
    width="500px"
    :before-close="handleClose"
    :close-on-click-modal="false"
  >
    <el-form
      ref="inventoryForm"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      label-position="left"
    >
      <!-- 物料信息显示 -->
      <div class="material-info-section">
        <h4>物料信息</h4>
        <div class="info-row">
          <span class="label">物料名称：</span>
          <span class="value">{{ materialInfo.materialName || '未知物料' }}</span>
        </div>
        <div class="info-row">
          <span class="label">当前重量：</span>
          <span class="value">{{ materialInfo.materialWeight || 0 }}T</span>
        </div>
        <div class="info-row">
          <span class="label">位置信息：</span>
          <span class="value">{{ materialInfo.position || '未知位置' }}</span>
        </div>
      </div>

      <!-- 盘点表单 -->
      <el-form-item label="盘点重量" prop="weight">
        <el-input-number
          v-model="formData.weight"
          :min="0"
          :precision="2"
          :step="0.1"
          placeholder="请输入盘点后的实际重量"
          style="width: 100%"
        >
          <template slot="append">T</template>
        </el-input-number>
        <div class="weight-tips">
          <span>请输入盘点后的实际重量（可以为0）</span>
        </div>
      </el-form-item>

      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="formData.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入盘点备注信息（可选）"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button
        type="primary"
        @click="handleSubmit"
        :loading="submitting"
      >
        确认盘点
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { stockInOrOut } from '@/api/wms/storehouse'

export default {
  name: 'InventoryDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    materialInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      submitting: false,
      formData: {
        weight: null,
        remark: ''
      },
      formRules: {
        weight: [
          { required: true, message: '请输入盘点重量', trigger: 'blur' },
          { type: 'number', min: 0, message: '重量不能小于0', trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.resetForm()
        // 默认设置为当前重量
        this.formData.weight = parseFloat(this.materialInfo.materialWeight) || 0
      }
    }
  },
  methods: {
    // 重置表单
    resetForm() {
      this.formData = {
        weight: null,
        remark: ''
      }
      if (this.$refs.inventoryForm) {
        this.$refs.inventoryForm.clearValidate()
      }
    },

    // 关闭弹窗
    handleClose() {
      this.$emit('update:visible', false)
      this.$emit('close')
    },

    // 提交表单
    async handleSubmit() {
      try {
        // 表单验证
        await this.$refs.inventoryForm.validate()

        this.submitting = true

        // 提交数据
        const submitData = {
          stockRealId: this.materialInfo.stockRealId, // 实际库存ID
          stackPlanId: this.materialInfo.stackPlanId, // 计划ID
          weight: this.formData.weight,
          remark: '盘点' // 固定字符串，后端根据此字段判断操作类型
        }

        console.log('盘点提交数据:', submitData);

        // 调用盘点API（使用出入库接口）
        const response = await stockInOrOut(submitData)

        if (response && response.code === 200) {
          this.$message.success('盘点操作成功')
          this.$emit('success', {
            type: 'inventory',
            data: submitData,
            response: response,
            userRemark: this.formData.remark
          })
          this.handleClose()
        }

      } catch (error) {
        console.error('盘点操作失败:', error)
        this.$message.error('盘点操作失败: ' + (error.message || '未知错误'))
      } finally {
        this.submitting = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.material-info-section {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 20px;

  h4 {
    margin: 0 0 12px 0;
    color: #303133;
    font-size: 14px;
    font-weight: 600;
  }

  .info-row {
    display: flex;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }

    .label {
      color: #606266;
      font-size: 13px;
      min-width: 80px;
    }

    .value {
      color: #303133;
      font-size: 13px;
      font-weight: 500;
    }
  }
}

.weight-tips {
  margin-top: 5px;
  font-size: 12px;
  color: #909399;
}

.dialog-footer {
  text-align: right;
}

// 表单样式优化
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-textarea__inner) {
  resize: none;
}
</style>
