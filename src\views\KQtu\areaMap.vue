<template>
  <div class="area-map">
    <!-- {{ x }} -->
    <!-- 横屏提示 -->
    <div v-if="isMobile" class="orientation-tip" :class="{ 'show': showOrientationTip }">
      <div class="tip-content">
        <div class="phone-icon">📱</div>
        <div class="tip-text">
          <h3>此页面仅支持横屏模式</h3>
          <p>请将您的设备旋转至横屏模式以继续使用</p>
          <p class="sub-tip">仓库地图需要更宽的显示空间</p>
        </div>
        <div class="rotate-icon">↻</div>
      </div>
    </div>

    <div class="title-header" :class="{ 'mobile': isMobile }">
      <div class="header-buttons">
        <el-button v-if="!isStackerOperator" class="header-btn" @click="refreshData" :size="isMobile ? 'small' : 'medium'">数据刷新</el-button>
        <el-button v-if="!isStackerOperator" class="header-btn" @click="toggleEdit" :size="isMobile ? 'small' : 'medium'">{{ isEditing ? '退出编辑' :
          '进入编辑' }}</el-button>
        <el-button class="header-btn" v-if="isEditing && !isStackerOperator" @click="saveData"
          :size="isMobile ? 'small' : 'medium'">保存</el-button>

        <el-button class="header-btn toggle-btn" @click="togglePanel"
          :size="isMobile ? 'small' : 'medium'">
          <i :class="panelVisible ? 'el-icon-s-fold' : 'el-icon-s-unfold'"></i>
          {{ isStackerOperator ? '任务面板' : '物料列表' }}
        </el-button>

        <el-button v-if="!isStackerOperator" class="header-btn stacker-toggle-btn" @click="toggleStackerDisplay"
          :size="isMobile ? 'small' : 'medium'"
          :type="showStackers ? 'success' : 'info'">
          {{ showStackers ? '隐藏大机' : '显示大机' }}
        </el-button>

      </div>
    </div>
    <div class="content-wrapper" :class="{ 'mobile': isMobile, 'tablet': isTablet, 'panel-open': panelVisible }">
      <div class="map-container" :class="{ 'panel-open': panelVisible }">
        <!-- WebSocket连接状态提示 -->
        <div v-if="loading" class="websocket-loading-global">
          <div class="loading-content">
            <i class="el-icon-loading"></i>
            <span>大机数据连接中...</span>
          </div>
        </div>

        <div class="warehouse-container">
          <div class="warehouse-row" v-for="row in warehouseRows" :key="row.id">
            <div class="row-label">{{ row.label }}</div>

            <div class="ruler-container">
              <div class="ruler">
                <div v-for="scale in scales" :key="scale" class="scale-mark" :class="{ 'no-number': scale === '700' }">
                  <div class="scale-line"></div>
                  <span v-if="scale !== '700'" class="scale-number">{{ scale }}</span>
                </div>
              </div>


              <div class="stacker-layer-for-row">
                <div v-if="showStackers" v-for="(stacker, location) in getStackersForRow(row.label)" :key="location"
                  :class="['stacker-container', `stacker-container-${location.toLowerCase()}`]"
                  :style="getStackerStyleForRuler(stacker, location)"
                  :title="`${stacker.name} - 刻度${stacker.position.scale}`">

                  <StackerReclaimerNew :data="stacker" :size="'medium'" :interactive="true" :container-mode="false"
                    @click="handleStackerClick" @hover="handleStackerHover" @leave="handleStackerLeave" />
                </div>
              </div>

              <div class="material-strip">
                <div v-for="(block, index) in row.blocks" :key="row.id + '_' + index" class="block-container" :class="{
                  'highlighted': isHighlighted(block.materialName),
                  'editable': isEditing,
                  'readonly': !isEditing
                }" :style="{
                  left: block.planLeft + 'px',
                  width: block.planWidth + 'px'
                }" @mouseover="handleUnifiedBlockHover($event, block, 'unified')"
                  @mouseleave="handleUnifiedBlockLeave">

                  <div class="plan-block" @mousedown.stop="handleMouseDown($event, block, row.label, 'plan')"
                    @touchstart.stop="handleTouchStart($event, block, row.label, 'plan')">
                    <div class="plan-content">
                      <div class="plan-title"
                        :title="`计划：${block.planName}${block.planWeight ? block.planWeight + 'T' : ''}`">
                        计划：{{ block.planName}}{{ block.planWeight ? block.planWeight + 'T' : ''
                        }}
                      </div>
                    </div>

                    <div v-if="isEditing" class="resize-handles">
                      <div class="handle left" @mousedown.stop="handleResizeMouseDown($event, block, 'left', 'plan')"
                        @touchstart.stop="handleResizeTouchStart($event, block, 'left', 'plan')"></div>
                      <div class="handle right" @mousedown.stop="handleResizeMouseDown($event, block, 'right', 'plan')"
                        @touchstart.stop="handleResizeTouchStart($event, block, 'right', 'plan')"></div>
                    </div>

                    <div class="material-block" :class="{
                      'highlighted': isHighlighted(block.planName),//block.materiaName 改为与计划名称一致 block.planName
                      'editable': isEditing,
                      'readonly': !isEditing,
                      'negative-weight': block.materialWeight < 0
                    }" :style="{
                      left: block.materialLeft + 'px',
                      width: block.materialWidth + 'px'
                    }" @mousedown.stop="handleMouseDown($event, block, null, 'material')"
                      @touchstart.stop="handleTouchStart($event, block, null, 'material')"
                      @contextmenu="isEditing ? showContextMenu($event, block) : null"
                      @touchend="handleTouchEnd($event, block)">
                      <div class="block-content">
                        <div class="block-title" :class="getBlockTitleClass(block)" :title="block.planName">
                          {{ getDisplayName(block.planName, block.materialWidth) }}
                        </div>
                        <div class="block-weight">{{ block.materialWeight }}T</div>
                      </div>
                      <div v-if="isEditing" class="resize-handles">
                        <div class="handle left"
                          @mousedown.stop="handleResizeMouseDown($event, block, 'left', 'material')"
                          @touchstart.stop="handleResizeTouchStart($event, block, 'left', 'material')"></div>
                        <div class="handle right"
                          @mousedown.stop="handleResizeMouseDown($event, block, 'right', 'material')"
                          @touchstart.stop="handleResizeTouchStart($event, block, 'right', 'material')"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>


        </div>
      </div>


    </div>

    <!-- 侧边面板 - 可收起展开，无遮罩 -->
    <div v-if="panelVisible" class="side-panel" :class="{ 'mobile': isMobile, 'tablet': isTablet }">
      <div class="panel-header">
        <h3>{{ isStackerOperator ? '大机任务面板' : '物料列表' }}</h3>
        <el-button size="mini" type="text" @click="togglePanel" class="close-btn">
          <i class="el-icon-close"></i>
        </el-button>
      </div>

      <!-- 大机任务面板内容 -->
      <div v-if="isStackerOperator" class="panel-task-content">
        <div v-if="filteredTaskList && filteredTaskList.length > 0" class="task-list">
          <div v-for="(task, index) in filteredTaskList" :key="task.id" class="task-item">
            <div class="task-header">
              <span class="task-number">任务 {{ index + 1 }}</span>
              <div class="status-value" :class="getStatusClass(task.workStatus)">
                {{ task.workStatus }}
              </div>
            </div>
            <div class="task-info">
              <div class="instruction-text">
                {{ task.workInfo }}
              </div>
            </div>
            <div class="task-actions">
              <!-- 未执行状态：显示开始执行按钮 -->
              <el-button
                v-if="task.workStatus === '未执行'"
                type="primary"
                size="small"
                @click="startTask(task)"
                :loading="task.confirming">
                开始执行
              </el-button>

              <!-- 正在执行状态：显示完成任务按钮 -->
              <el-button
                v-else-if="task.workStatus === '正在执行'"
                type="success"
                size="small"
                @click="completeTask(task)"
                :loading="task.confirming">
                完成任务
              </el-button>
            </div>
          </div>
        </div>
        <div v-else class="no-task">
          <i class="el-icon-info"></i>
          <span>暂无指令</span>
        </div>
      </div>

      <!-- 物料列表内容 -->
      <div v-else class="panel-material-content">
        <div class="info-list">
          <div class="info-item-header">
            <span class="col-name">物料名称</span>
            <span class="col-position">位置</span>
            <span class="col-weight">重量(T)</span>
          </div>
          <div class="info-item-body">
            <div class="info-item" v-for="(item, index) in materialList" :key="'material_' + index"
              :class="{ 'highlighted': isHighlighted(item.name) }" @mouseover="handleItemHover(item.name)"
              @mouseleave="handleItemLeave">
              <span class="material-name" :title="item.name">{{ item.name }}</span>
              <span class="data-position">{{ item.endPosition }} - {{ item.startPosition }}</span>
              <span class="data-weight">{{ item.weight }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 统一悬浮提示 -->
    <div v-show="unifiedTooltip.visible" class="unified-tooltip" :style="{
      left: unifiedTooltip.x + 'px',
      top: unifiedTooltip.y + 'px'
    }">
      <div class="tooltip-content">
        <!-- 计划信息 -->
        <div class="plan-section" v-if="unifiedTooltip.planData">
          <div class="section-header">
            <span class="section-type plan-type">计划</span>
            <div class="section-title">{{ unifiedTooltip.planData.name }}</div>
          </div>
          <div class="section-info">
            <div class="info-row">
              <span class="label">计划重量:</span>
              <span class="value">{{ unifiedTooltip.planData.weight }}T</span>
            </div>
          </div>
        </div>

        <!-- 分隔线 -->
        <div v-if="unifiedTooltip.planData && unifiedTooltip.materialData" class="section-divider"></div>

        <!-- 物料信息 -->
        <div class="material-section" v-if="unifiedTooltip.materialData">
          <div class="section-header">
            <span class="section-type material-type">实际</span>
            <div class="section-title">{{ unifiedTooltip.materialData.name }}</div>
          </div>
          <div class="section-info">
            <div class="info-row">
              <span class="label">实际重量:</span>
              <span class="value">{{ unifiedTooltip.materialData.weight }}T</span>
            </div>
            <div class="info-row">
              <span class="label">位置:</span>
              <span class="value">{{ unifiedTooltip.materialData.position }}</span>
            </div>
          </div>
        </div>



        <!-- 仅有计划时的提示 -->
        <div v-if="unifiedTooltip.planData && !unifiedTooltip.materialData" class="plan-only-section">
          <div class="plan-status">
            <span class="status-label">状态:</span>
            <span class="status-value empty">无物料</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 右键菜单 -->
    <div v-show="contextMenuVisible" class="context-menu" :style="{
      left: contextMenuX + 'px',
      top: contextMenuY + 'px'
    }">
      <div class="menu-item" @click="handleMenuClick('in')">
        <i class="el-icon-upload2"></i>
        入库
      </div>
      <div class="menu-item" @click="handleMenuClick('out')">
        <i class="el-icon-download"></i>
        出库
      </div>
      <div class="menu-item" @click="handleMenuClick('changeStack')">
        <i class="el-icon-refresh"></i>
        盘点
      </div>
      <div class="menu-item" @click="handleMenuClick('delete')">
        <i class="el-icon-delete"></i>
        删除
      </div>
    </div>

    <!-- 出库、入库弹窗 -->
    <StockOperationDialog :visible.sync="stockDialogVisible" :operation-type="stockOperationType"
      :material-info="currentStockMaterial" @success="handleStockOperationSuccess" @close="handleStockDialogClose" />

    <!-- 盘点弹窗 -->
    <InventoryDialog :visible.sync="inventoryDialogVisible"
      :material-info="currentInventoryMaterial" @success="handleInventorySuccess" @close="handleInventoryDialogClose" />
  </div>
</template>

<script>

import { getStorehouseMapData_1, updateStorehouseMapData, stockInOrOut, deleteStock, getStorehouseMapData_2, getStackerTasks_1, getStackerTasks_2, updateStackerTaskStatus } from "@/api/wms/storehouse";
import StockOperationDialog from './components/StockOperationDialog.vue';
import InventoryDialog from './components/InventoryDialog.vue';
import StackerReclaimerNew from './components/StackerReclaimerNew.vue';
export default {
  name: 'AreaMap',
  components: {
    StockOperationDialog,
    InventoryDialog,
    StackerReclaimerNew
  },
  props: {
    stockyardId: {
      type: String,
      required: true,
      validator: (value) => ['stockyard1', 'stockyard2'].includes(value)
    }
  },
  data() {
    return {
      x: null,
      loading: false, // WebSocket连接状态
      // socket参数
      socket: null,
      timeout: 10 * 1000, // 45秒一次心跳
      timeoutObj: null, // 心跳心跳倒计时
      serverTimeoutObj: null, // 心跳倒计时
      timeoutnum: null, // 断开 重连倒计时
      lockReconnect: false,
      websocket: null,
      websocketId: null, // WebSocket 连接标识符，用于区分不同的 stockyard

      scales: ['700', '650', '600', '550', '500', '450', '400', '350', '300', '250', '200', '150', '100', '50', '0'],

      isMobile: false,
      isTablet: false,
      showMaterialList: true,
      panelVisible: false, // 侧边面板显示状态
      showStackers: true, // 控制大机显示/隐藏
      showStackersBeforeEdit: true, // 保存编辑前的大机显示状态
      touchStartTime: null,
      showOrientationTip: false, // 控制横屏提示显示
      orientationCheckInterval: null, // 方向检测定时器
      // 悬浮提示
      unifiedTooltip: {
        visible: false,
        x: 0,
        y: 0,
        planData: null,
        materialData: null,
        currentBlockId: null // 记录当前悬停的块ID
      },
      isDragging: false,
      materialTooltip: {
        visible: false,
        x: 0,
        y: 0,
        name: '',
        weight: '',
        position: ''
      },
      isEditMode: false,
      warehouseRows: [
        {
          id: 5,
          label: 'E',
          blocks: [
            {
              id: 'E1',
              materialName: '焦粉',
              planWeight: 621,
              materialWeight: 621,
              planLeft: 50,
              planWidth: 150,
              materialLeft: 10,
              materialWidth: 100,
              position: '581-621'
            },
            {
              id: 'E2',
              materialName: '纽曼块矿下粉',
              planWeight: 621,
              materialWeight: 621,
              planLeft: 250,
              planWidth: 150,
              materialLeft: 10,
              materialWidth: 100,
              position: '581-621'
            },
            {
              id: 'E3',
              materialName: '南非块矿62+',
              planWeight: 621,
              materialWeight: 621,
              planLeft: 450,
              planWidth: 150,
              materialLeft: 10,
              materialWidth: 100,
              position: '581-621'
            }
          ]
        },
        {
          id: 4,
          label: 'D',
          blocks: [
            {
              id: 'D1',
              materialName: '萤石',
              planWeight: 621,
              materialWeight: 621,
              planLeft: 100,
              planWidth: 200,
              materialLeft: 10,
              materialWidth: 150,
              position: '581-621'
            },
            {
              id: 'D2',
              materialName: '澳大利亚SP10块矿',
              planWeight: 621,
              materialWeight: 621,
              planLeft: 350,
              planWidth: 200,
              materialLeft: 10,
              materialWidth: 150,
              position: '581-621'
            }
          ]
        },
        {
          id: 3,
          label: 'C',
          blocks: [
            {
              id: 'C1',
              materialName: '物料名称5',
              planWeight: 500,
              materialWeight: 500,
              planLeft: 50,
              planWidth: 150,
              materialLeft: 10,
              materialWidth: 100,
              position: '-'
            },
            {
              id: 'C2',
              materialName: '物料名称5',
              planWeight: 500,
              materialWeight: 500,
              planLeft: 250,
              planWidth: 150,
              materialLeft: 10,
              materialWidth: 100,
              position: '-'
            },
            {
              id: 'C3',
              materialName: '物料名称5',
              planWeight: 500,
              materialWeight: 500,
              planLeft: 450,
              planWidth: 150,
              materialLeft: 10,
              materialWidth: 100,
              position: '-'
            }
          ]
        },
        {
          id: 2,
          label: 'B',
          blocks: [
            {
              id: 'B1',
              materialName: '物料名称5',
              planWeight: 500,
              materialWeight: 500,
              planLeft: 100,
              planWidth: 150,
              materialLeft: 10,
              materialWidth: 100,
              position: '-'
            },
            {
              id: 'B2',
              materialName: '物料名称5',
              planWeight: 500,
              materialWeight: 500,
              planLeft: 300,
              planWidth: 150,
              materialLeft: 10,
              materialWidth: 100,
              position: '-'
            },
            {
              id: 'B3',
              materialName: '物料名称5',
              planWeight: 500,
              materialWeight: 500,
              planLeft: 500,
              planWidth: 150,
              materialLeft: 10,
              materialWidth: 100,
              position: '-'
            }
          ]
        },
        {
          id: 1,
          label: 'A',
          blocks: [
            {
              id: 'A1',
              materialName: '物料名称5',
              planWeight: 500,
              materialWeight: 500,
              planLeft: 50,
              planWidth: 200,
              materialLeft: 10,
              materialWidth: 150,
              position: '-'
            },
            {
              id: 'A2',
              materialName: '物料名称5',
              planWeight: 500,
              materialWeight: 500,
              planLeft: 300,
              planWidth: 200,
              materialLeft: 10,
              materialWidth: 150,
              position: '-'
            },
            {
              id: 'A3',
              materialName: '物料名称5',
              planWeight: 500,
              materialWeight: 500,
              planLeft: 550,
              planWidth: 200,
              materialLeft: 10,
              materialWidth: 150,
              position: '-'
            }
          ]
        }
      ],

      dragState: {
        isDragging: false,
        currentBlock: null,
        startX: 0,
        startLeft: 0,
        isResizing: false,
        resizeDirection: null,
        type: null
      },
      hoveredMaterial: null,
      pixelScaleRatio: 2,
      maxScale: 700,
      resizeTimer: null,
      containerWidth: 0,
      contextMenuVisible: false,
      contextMenuX: 0,
      contextMenuY: 0,
      currentContextBlock: null,
      mutationObserver: null,
      editingItem: null,
      editingStartPosition: 0,
      editingEndPosition: 0,
      originalEditingItem: null,
      resizeInProgress: false,
      isEditing: false,
      lastWindowWidth: 0,
      lastWindowHeight: 0,
      windowResizeInProgress: false,
      dragPerformanceStats: {
        totalDragEvents: 0,
        throttledDragEvents: 0,
        lastDragTime: 0
      },
      stockDialogVisible: false,
      stockOperationType: 'in',
      currentStockMaterial: {},
      inventoryDialogVisible: false, // 盘点弹窗显示状态
      currentInventoryMaterial: {}, // 当前盘点的物料信息
      autoRefreshTimer: null,
      autoRefreshInterval: 5000,

      // 大机任务相关数据
      taskList: [], // 大机任务列表
      taskListTimer: null, // 大机任务列表定时器
      taskListInterval: 30000, // 30秒刷新间隔



      stackerReclaimers: {
        AB: {
          id: 'sr-001',
          name: '堆取料机#1',
          position: {
            location: 'AB',
            scale: 350,
            targetScale: 350
          },
          equipment: {
            status: 'idle',
            boomAngle: 0,
            targetBoomAngle: 0,
            bucketWheelSpeed: 0,
            targetBucketWheelSpeed: 0
          },
          operation: {
            mode: 'idle',
            targetMaterial: '',
            isMoving: false,
            isRotating: false,
            lastUpdateTime: Date.now()
          }
        },
        BC: {
          id: 'sr-002',
          name: '堆取料机#2',
          position: {
            location: 'BC',
            scale: 450,
            targetScale: 450
          },
          equipment: {
            status: 'working',
            boomAngle: 30,
            targetBoomAngle: 30,
            bucketWheelSpeed: 120,
            targetBucketWheelSpeed: 120
          },
          operation: {
            mode: 'stacking',
            targetMaterial: '混匀矿',
            isMoving: false,
            isRotating: false,
            lastUpdateTime: Date.now()
          }
        },
        CD: {
          id: 'sr-003',
          name: '堆取料机#3',
          position: {
            location: 'CD',
            scale: 500,
            targetScale: 500
          },
          equipment: {
            status: 'moving',
            boomAngle: -15,
            targetBoomAngle: -15,
            bucketWheelSpeed: 0,
            targetBucketWheelSpeed: 0
          },
          operation: {
            mode: 'idle',
            targetMaterial: '',
            isMoving: false,
            isRotating: false,
            lastUpdateTime: Date.now()
          }
        },
        DE: {
          id: 'sr-004',
          name: '堆取料机#4',
          position: {
            location: 'DE',
            scale: 600,
            targetScale: 600
          },
          equipment: {
            status: 'working',
            boomAngle: 45,
            targetBoomAngle: 45,
            bucketWheelSpeed: 120,
            targetBucketWheelSpeed: 120
          },
          operation: {
            mode: 'reclaiming',
            targetMaterial: '焦粉',
            isMoving: false,
            isRotating: false,
            lastUpdateTime: Date.now()
          }
        }
      }
    }
  },

  // 💡 纯数据驱动 - 不需要监听器，Vue自动响应数据变化

  created() {
    // var websocket = null;
    // websocket = new WebSocket("ws://127.0.0.1:8006/api/websocket");


    // //连接发生错误的回调方法
    // websocket.onerror = function () {
    //   console.log("websocket.onerror: WebSocket连接发生错误");
    // };

    // //连接成功建立的回调方法
    // websocket.onopen = function () {
    //   console.log("websocket.onopen: WebSocket连接成功");
    //   websocket.send('ok');
    // }

    // //接收到消息的回调方法
    // websocket.onmessage = function (event) {
    //   // console.log("websocket.onmessage: " + event.data);
    //   console.log(event.data);
    //   let data = event.data;
    //   $("#online").html(data);

    //   // this.getMessage(event.data);
    // }

    // //连接关闭的回调方法
    // websocket.onclose = function () {
    //   console.log("websocket.onclose: WebSocket连接关闭");
    // }

    // //监听窗口关闭事件，当窗口关闭时，主动去关闭websocket连接，防止连接还没断开就关闭窗口，server端会抛异常。
    // window.onbeforeunload = function () {
    //   websocket.send('no');
    //   websocket.close();
    // }

    this.detectDeviceType();

    if (this.isMobile) {
      this.forceInitialLandscape();
    }

    this.debouncedResize = this.debounce(() => {
      this.handleWindowResize();
    }, 500);

    this.checkWindowChange = () => {
      const currentWidth = window.innerWidth;
      const currentHeight = window.innerHeight;

      if (this.lastWindowWidth === 0 || this.lastWindowHeight === 0) {
        this.lastWindowWidth = currentWidth;
        this.lastWindowHeight = currentHeight;
        return;
      }

      const widthChange = Math.abs(currentWidth - this.lastWindowWidth);
      const heightChange = Math.abs(currentHeight - this.lastWindowHeight);

      if (widthChange > 10 || heightChange > 10) {
        this.debouncedResize();
      }
      this.lastWindowWidth = currentWidth;
      this.lastWindowHeight = currentHeight;
    };

    this.debouncedContainerResize = this.debounce(() => {
      this.handleContainerResize();
    }, 200);

    this.planDragAnimationFrame = null;
    this.materialDragAnimationFrame = null;
    this.lastPlanDragEvent = null;
    this.lastMaterialDragEvent = null;

    this.throttledPlanDrag = (event) => {
      this.lastPlanDragEvent = event;
      if (!this.planDragAnimationFrame) {
        this.planDragAnimationFrame = requestAnimationFrame(() => {
          if (this.lastPlanDragEvent) {
            this.handlePlanDragCore(this.lastPlanDragEvent);
            this.lastPlanDragEvent = null;
          }
          this.planDragAnimationFrame = null;
        });
      }
    };
    this.throttledMaterialDrag = (event) => {
      this.lastMaterialDragEvent = event;
      if (!this.materialDragAnimationFrame) {
        this.materialDragAnimationFrame = requestAnimationFrame(() => {
          if (this.lastMaterialDragEvent) {
            this.handleMaterialDragCore(this.lastMaterialDragEvent);
            this.lastMaterialDragEvent = null;
          }
          this.materialDragAnimationFrame = null;
        });
      }
    };

    // 监听窗口大小变化（包括菜单栏展开收缩）
    window.addEventListener('resize', this.checkWindowChange);

    window.addEventListener('orientationchange', this.handleOrientationChange);

    document.addEventListener('mousedown', this.handleGlobalMouseDown);
    document.addEventListener('touchstart', this.handleGlobalTouchStart);

    document.addEventListener('mouseup', this.handleGlobalMouseUp);
    document.addEventListener('touchend', this.handleGlobalTouchEnd);
  },

  // keep-alive 组件激活时触发
  activated() {
    console.log(`页面激活，stockyardId: ${this.stockyardId}`);

    // 如果需要显示大机且WebSocket未连接，则重新连接
    if (this.showStackers && !this.websocket) {
      console.log('页面激活时重新连接WebSocket');
      this.initWebSocket(1);
    }

    // 重新启动自动刷新
    if (!this.autoRefreshTimer) {
      this.startAutoRefresh();
    }
  },

  // keep-alive 组件失活时触发（包括标签页关闭、切换到其他页面）
  deactivated() {
    console.log(`页面失活，关闭 WebSocket 连接 [${this.websocketId}]`);

    // 关闭WebSocket连接
    this.disconnectWebSocket();

    // 停止自动刷新
    this.stopAutoRefresh();

    // 隐藏所有提示框
    this.hideUnifiedTooltip();
    this.hideContextMenu();
  },

  beforeDestroy() {
    console.log("beforeDestroy")

    // 停止大机任务列表定时器
    this.stopTaskListTimer();

    // 移除 resize 事件监听器
    window.removeEventListener('resize', this.checkWindowChange);
    // 移除方向变化监听器
    window.removeEventListener('orientationchange', this.handleOrientationChange);
    // 移除全局事件监听器
    document.removeEventListener('mousedown', this.handleGlobalMouseDown);
    document.removeEventListener('touchstart', this.handleGlobalTouchStart);
    document.removeEventListener('mouseup', this.handleGlobalMouseUp);
    document.removeEventListener('touchend', this.handleGlobalTouchEnd);


    if (this.resizeTimer) {
      cancelAnimationFrame(this.resizeTimer);
      this.resizeTimer = null;
    }


    if (this.planDragAnimationFrame) {
      cancelAnimationFrame(this.planDragAnimationFrame);
      this.planDragAnimationFrame = null;
    }
    if (this.materialDragAnimationFrame) {
      cancelAnimationFrame(this.materialDragAnimationFrame);
      this.materialDragAnimationFrame = null;
    }


    this.stopAutoRefresh();

    // 移除 ResizeObserver
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
      this.resizeObserver = null;
    }


    if (this.mutationObserver) {
      this.mutationObserver.disconnect();
      this.mutationObserver = null;
    }


    this.cleanupDragListeners();

    // 移除全局事件监听
    document.removeEventListener('click', this.hideContextMenu);

    // 解锁屏幕方向
    if (this.isMobile) {
      this.unlockOrientation();
    }

    //  清理 WebSocket 和关闭标签页监听
    if (this.websocket) {
      console.log(`组件销毁，关闭 WebSocket 连接 [${this.websocketId}]`);
      this.websocket.close();
      this.websocket = null;
    }
    this.websocketId = null;
    window.onbeforeunload = null;
  },

  // 监听 stockyardId 参数变化
  watch: {
    'stockyardId': {
      handler(newId, oldId) {
        // 当 stockyardId 发生变化时，重新加载数据
        if (newId !== oldId && newId) {
          this.initializeData();
        }
      },
      immediate: false // 不在初始化时立即执行，因为mounted已经会调用initializeData
    }
  },

  async mounted() {
    // 注意：WebSocket连接现在在activated生命周期中初始化，以支持keep-alive
    window.addEventListener('onmessageWS', this.getSocketData)

    this.lastWindowWidth = window.innerWidth;
    this.lastWindowHeight = window.innerHeight;

    if (this.isMobile) {
      this.lockLandscapeOrientation();
    }

    await this.initializeData();

    // 如果是tgylkgl角色，加载大机任务列表并启动定时器
    if (this.isStackerOperator) {
      await this.loadTaskList();
      this.startTaskListTimer();
    }

    this.ensureStackerDataTypes();

    this.$nextTick(() => {
      this.resizeObserver = new ResizeObserver(entries => {
        this.debouncedContainerResize();
      });

      const container = this.$el.querySelector('.ruler-container');
      if (container) {
        this.resizeObserver.observe(container);
      }

      this.initializeContainer();
      this.validateInitialization();
    });

    document.addEventListener('click', this.hideContextMenu);
    this.setupSidebarWatcher();
    // 自动刷新现在在activated生命周期中启动
  },

  methods: {

    initWebSocket(supplierId) {
      //  根据 stockyard 类型区分 WebSocket 连接
      const stockyardId = this.stockyardId;
      let wsUrl;

      if (stockyardId === 'stockyard1') {
        wsUrl = `ws://127.0.0.1:8006/api/websocket`;
      } else if (stockyardId === 'stockyard2') {
        wsUrl = `ws://127.0.0.1:8006/api/websocket`;
      } 

      console.log(`初始化 WebSocket 连接: ${wsUrl}`);

      //  设置 WebSocket 标识符
      this.websocketId = stockyardId || 'default';

      // 设置loading状态，显示连接中提示
      this.loading = true;
      console.log('WebSocket连接中，设置loading=true');

      this.websocket = new WebSocket(wsUrl);
      this.websocket.onopen = this.websocketonopen;
      this.websocket.onerror = this.websocketonerror;
      this.websocket.onmessage = this.setOnmessageMessage;
      this.websocket.onclose = this.websocketclose;

      //  监听窗口关闭事件，当窗口关闭时，主动去关闭websocket连接，防止连接还没断开就关闭窗口，server端会抛异常。
      window.onbeforeunload = () => {
        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
          console.log(`页面关闭，断开 WebSocket 连接 [${this.websocketId}]`);
          this.websocket.send('no');
          this.websocket.close();
        }
      }
    },
    start() {
     // console.log('start');
      //清除延时器
      this.timeoutObj && clearTimeout(this.timeoutObj);
      this.serverTimeoutObj && clearTimeout(this.serverTimeoutObj);
      this.timeoutObj = setTimeout(() => {
        if (this.websocket && this.websocket.readyState == 1) {
          this.websocket.send('heartBath');//发送消息，服务端返回信息，即表示连接良好，可以在socket的onmessage事件重置心跳机制函数
        } else {
          this.reconnect();
        }
        //定义一个延时器等待服务器响应，若超时，则关闭连接，重新请求server建立socket连接
        this.serverTimeoutObj = setTimeout(() => {
          this.websocket.close();
        }, this.timeout)
      }, this.timeout)
    },
    reset() { // 重置心跳
      // 清除时间
      clearTimeout(this.timeoutObj);
      clearTimeout(this.serverTimeoutObj);
      // 重启心跳
      this.start();
    },
    // 重新连接
    reconnect() {
      if (this.lockReconnect) return
      this.lockReconnect = true;
      //没连接上会一直重连，设置延迟避免请求过多
      this.timeoutnum && clearTimeout(this.timeoutnum);
      this.timeoutnum = setTimeout(() => {
        this.initWebSocket();
        this.lockReconnect = false;
      }, 5000)
    },
    async setOnmessageMessage(event) {
      // console.log(event.data, '获得消息');
      this.reset();
      // 自定义全局监听事件
      window.dispatchEvent(new CustomEvent('onmessageWS', {
        detail: {
          data: event.data
        }
      }))
      // //发现消息进入    开始处理前端触发逻辑
      // if (event.data === 'success' || event.data === 'heartBath') return
    },
    websocketonopen() {
      //开启心跳
      this.start();
      console.log(`WebSocket连接成功!!! [${this.websocketId}] ${new Date()} ---- ${this.websocket.readyState}`);

      // 连接成功，但保持loading状态，等待接收到有效数据后再关闭
      console.log('WebSocket连接成功，等待接收数据...');
    },
    websocketonerror(e) {
      console.log("WebSocket连接发生错误" + e);

      // 连接失败，关闭loading状态
      this.loading = false;
      console.log('WebSocket连接失败，关闭loading状态');
    },
    websocketclose(e) {
      if (this.websocket) {
        this.websocket.close();
        this.websocket = null;
      }
      clearTimeout(this.timeoutObj);
      clearTimeout(this.serverTimeoutObj);
      console.log(`WebSocket连接关闭 [${this.websocketId}]`);
    },
    websocketsend(messsage) {
      if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
        this.websocket.send(messsage);
      }
    },
    closeWebSocket() { // 关闭websocket
      if (this.websocket) {
        this.websocket.close();
        this.websocket = null;
      }
    },

    // 收到消息处理
    getSocketData(res) {
      //  只有在显示大机时才处理 WebSocket 数据
      if (!this.showStackers) {
        return;
      }

      this.x = res.detail.data

      // 过滤心跳和状态消息，只处理实际的位置数据
      if (this.x === 'success' || this.x === 'heartBath') {
        console.log('收到WebSocket心跳/状态消息:', this.x);
        return; // 心跳消息不是有效数据，直接返回
      }

      let p = 0
      const parsedValue = parseInt(this.x)

      // 验证解析的数值是否有效
      if (!isNaN(parsedValue)) {
        p = parsedValue

        // 只有接收到真正的位置数据，关闭loading状态
        if (this.loading) {
          this.loading = false;
          console.log('接收到WebSocket有效位置数据:', p, '关闭loading状态');
        }
      } else {
        console.warn('WebSocket接收到无效数据:', this.x)
        return; // 如果数据无效，直接返回不更新大机位置
      }

      //  DE 大机：同时移动位置和旋转大臂
      this.stackerReclaimers.DE.position.scale = p
      this.stackerReclaimers.DE.equipment.boomAngle = p / 5; // 大臂角度为位置的1/10

      this.stackerReclaimers.CD.equipment.boomAngle = p;

      this.stackerReclaimers.BC.equipment.boomAngle = p;

      this.stackerReclaimers.AB.position.scale = p;
      // this.stackerReclaimers.DE.equipment.boomAngle = 45;

      // this.stackerReclaimers.DE.position.boomAngle = parseInt(this.x)

      // if (res.detail.data === 'success' || res.detail.data === 'heartBath') return
      // ...业务处理
    },




    forceInitialLandscape() {
      document.documentElement.classList.add('force-landscape');
      document.body.classList.add('force-landscape');
      this.setLandscapeViewport();
      this.disablePageInteractions();
    },

    setLandscapeViewport() {
      let viewport = document.querySelector('meta[name="viewport"]');
      if (!viewport) {
        viewport = document.createElement('meta');
        viewport.name = 'viewport';
        document.head.appendChild(viewport);
      }

      viewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, orientation=landscape';
    },

    disablePageInteractions() {
      document.addEventListener('touchstart', function (event) {
        if (event.touches.length > 1) {
          event.preventDefault();
        }
      }, { passive: false });
      let lastTouchEnd = 0;
      document.addEventListener('touchend', function (event) {
        const now = (new Date()).getTime();
        if (now - lastTouchEnd <= 300) {
          event.preventDefault();
        }
        lastTouchEnd = now;
      }, { passive: false });

      // 禁用页面滚动（除了指定区域）
      document.addEventListener('touchmove', function (event) {

        const allowScrollElements = [
          '.material-info',
          '.info-item-body',
          '.warehouse-container'
        ];

        let allowScroll = false;
        for (let selector of allowScrollElements) {
          if (event.target.closest(selector)) {
            allowScroll = true;
            break;
          }
        }

        if (!allowScroll) {
          event.preventDefault();
        }
      }, { passive: false });
    },

    detectDeviceType() {
      const userAgent = navigator.userAgent;
      const screenWidth = window.innerWidth;
      const screenHeight = window.innerHeight;

      // 检测是否为移动设备
      this.isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent) || screenWidth <= 768;
      this.isTablet = /iPad|Android/i.test(userAgent) && screenWidth > 768 && screenWidth <= 1024;

      // 检测屏幕方向
      this.checkOrientation();




      if (this.isMobile) {
        this.showMaterialList = false;
      }
    },

    // 检测屏幕方向
    checkOrientation() {
      if (!this.isMobile) {
        this.showOrientationTip = false;
        return;
      }

      const orientation = this.getOrientation();


      this.showOrientationTip = orientation === 'portrait';

    },


    getOrientation() {

      if (screen.orientation) {
        return screen.orientation.angle === 0 || screen.orientation.angle === 180 ? 'portrait' : 'landscape';
      }


      if (typeof window.orientation !== 'undefined') {
        return Math.abs(window.orientation) === 90 ? 'landscape' : 'portrait';
      }


      return window.innerWidth > window.innerHeight ? 'landscape' : 'portrait';
    },

    handleOrientationChange() {

      setTimeout(() => {

        this.debouncedResize();
      }, 400); // 增加延迟确保方向变化完成
    },

    toggleMaterialList() {
      this.showMaterialList = !this.showMaterialList;

      this.$nextTick(() => {
        this.debouncedContainerResize();
      });
    },

    // ==================== 大机任务定时器相关方法 ====================

    // 启动大机任务列表定时器
    startTaskListTimer() {
      if (this.taskListTimer) {
        clearInterval(this.taskListTimer);
      }

      this.taskListTimer = setInterval(() => {
        if (this.isStackerOperator) {
          this.loadTaskList();
        }
      }, this.taskListInterval);

      console.log('大机任务列表定时器已启动，每30秒刷新一次');
    },

    // 停止大机任务列表定时器
    stopTaskListTimer() {
      if (this.taskListTimer) {
        clearInterval(this.taskListTimer);
        this.taskListTimer = null;
        console.log('大机任务列表定时器已停止');
      }
    },

    // 获取任务状态对应的CSS类名
    getStatusClass(workStatus) {
      switch (workStatus) {
        case '未执行':
          return 'status-pending';
        case '正在执行':
          return 'status-executing';
        case '已完成':
          return 'status-completed';
        default:
          return 'status-unknown';
      }
    },

    // 切换侧边面板显示状态
    togglePanel() {
      this.panelVisible = !this.panelVisible;

      // 面板状态变化后重新刷新数据，确保布局正确
      this.$nextTick(() => {
        setTimeout(() => {
          // 等待CSS动画完成后重新刷新数据
          // 第一个参数false表示不是自动刷新，第二个参数true表示是面板切换触发
          this.refreshData(false, true);
        }, 350); // 等待面板动画完成
      });
    },

    // 切换大机显示/隐藏
    toggleStackerDisplay() {
      this.showStackers = !this.showStackers;

      if (this.showStackers) {
        // 显示大机时，重新连接 WebSocket
        this.initWebSocket();
        this.$message.success('大机显示已开启，WebSocket 连接已建立');
      } else {
        // 隐藏大机时，断开 WebSocket 连接
        this.disconnectWebSocket();
        this.$message.info('大机显示已关闭，WebSocket 连接已断开');
      }
    },

    // 断开 WebSocket 连接
    disconnectWebSocket() {
      if (this.websocket) {
        // 发送关闭消息
        if (this.websocket.readyState === WebSocket.OPEN) {
          this.websocket.send('no');
        }
        // 关闭连接
        this.websocket.close();
        this.websocket = null;

        // 清理定时器
        if (this.timeoutObj) {
          clearTimeout(this.timeoutObj);
          this.timeoutObj = null;
        }
        if (this.serverTimeoutObj) {
          clearTimeout(this.serverTimeoutObj);
          this.serverTimeoutObj = null;
        }

        console.log(`WebSocket 连接已主动断开 [${this.websocketId}]`);

        // 清理连接标识
        this.websocketId = null;
      }
    },

    toggleEditMode() {
      this.isEditMode = !this.isEditMode;

      // 退出编辑模式时隐藏所有提示
      if (!this.isEditMode) {
        this.hideMaterialTooltip();
        this.hideContextMenu();
      }



      // 可以在这里添加编辑模式切换的其他逻辑
      this.$message({
        type: 'success',
        message: this.isEditMode ? '已进入编辑模式' : '已退出编辑模式'
      });
    },


    async lockLandscapeOrientation() {
      try {

        if (screen.orientation && screen.orientation.lock) {
          await screen.orientation.lock('landscape-primary');

          return;
        }

        if (screen.lockOrientation) {
          screen.lockOrientation('landscape');

          return;
        }


        if (screen.mozLockOrientation) {
          screen.mozLockOrientation('landscape');

          return;
        }

        if (screen.msLockOrientation) {
          screen.msLockOrientation('landscape');

          return;
        }

      } catch (error) {

      }

      // 启用强制横屏检测
      this.enableForceLandscape();
    },

    // 启用强制横屏模式
    enableForceLandscape() {
      // 持续检测屏幕方向
      this.orientationCheckInterval = setInterval(() => {
        this.checkOrientation();
      }, 500);

      // 立即检测
      this.checkOrientation();

      // 添加全屏API支持
      this.requestFullscreenIfNeeded();
    },

    // 请求全屏
    requestFullscreenIfNeeded() {
      if (!this.isMobile) return;

      const element = document.documentElement;


      setTimeout(() => {
        try {
          if (element.requestFullscreen) {
            element.requestFullscreen();
          } else if (element.webkitRequestFullscreen) {
            element.webkitRequestFullscreen();
          } else if (element.mozRequestFullScreen) {
            element.mozRequestFullScreen();
          } else if (element.msRequestFullscreen) {
            element.msRequestFullscreen();
          }
        } catch (error) {

        }
      }, 1000);
    },

    // 解锁屏幕方向
    unlockOrientation() {
      try {

        if (this.orientationCheckInterval) {
          clearInterval(this.orientationCheckInterval);
          this.orientationCheckInterval = null;
        }

        // 解锁屏幕方向
        if (screen.orientation && screen.orientation.unlock) {
          screen.orientation.unlock();

        } else if (screen.unlockOrientation) {
          screen.unlockOrientation();
        } else if (screen.mozUnlockOrientation) {
          screen.mozUnlockOrientation();
        } else if (screen.msUnlockOrientation) {
          screen.msUnlockOrientation();
        }

        // 退出全屏
        if (document.exitFullscreen) {
          document.exitFullscreen();
        } else if (document.webkitExitFullscreen) {
          document.webkitExitFullscreen();
        } else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen();
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen();
        }
      } catch (error) {

      }
    },


    async initializeData() {
      try {
        this.loading = true;

        // 清空旧数据
        this.warehouseRows = [];

        const id = this.stockyardId;
        let response;

        if (id === 'stockyard1') {
          response = await getStorehouseMapData_1();
        } else if (id === 'stockyard2') {
          response = await getStorehouseMapData_2();
        }

        if (response && response.data && response.code === 200) {
          this.updateWarehouseDataFromAPI(response.data);
          // this.$message.success('数据加载成功');
        } else {
          this.$message.warning('API数据异常，使用默认数据');
        }
      } catch (apiError) {
        this.$message.warning('API调用失败，使用默认数据: ' + apiError.message);
      } finally {
        this.loading = false;
      }
    },

    handleContainerResize() {



      if (this.resizeInProgress) {

        return;
      }

      this.resizeInProgress = true;

      this.$nextTick(() => {
        try {
          const container = this.$el?.querySelector('.ruler-container');
          if (!container) {

            return;
          }

          const newContainerWidth = container.clientWidth;


          const tolerance = 2;
          if (newContainerWidth > 0 && Math.abs(newContainerWidth - this.containerWidth) > tolerance) {
            this.recalculatePixelPositions(newContainerWidth);
          }
        } finally {

          setTimeout(() => {
            this.resizeInProgress = false;
          }, 100);
        }
      });
    },

    // 改进的resize处理
    handleResize() {
      if (this.resizeTimer) {
        cancelAnimationFrame(this.resizeTimer);
      }

      this.resizeTimer = requestAnimationFrame(() => {
        const container = this.$el?.querySelector('.ruler-container');
        if (!container) return;

        const newContainerWidth = container.clientWidth;

        if (newContainerWidth === 0) {

          return;
        }


        if (newContainerWidth === this.containerWidth) {
          return;
        }


        this.recalculatePixelPositions(newContainerWidth);
      });
    },


    recalculatePixelPositions(newContainerWidth) {
      try {



        if (newContainerWidth <= 0 || newContainerWidth > 10000) {
          console.warn('无效的容器宽度:', newContainerWidth);
          return;
        }


        const fixedScalePositions = this.saveFixedScalePositions();

        if (!fixedScalePositions || fixedScalePositions.length === 0) {
          console.warn('没有有效的刻度数据，跳过重新计算');
          return;
        }


        this.containerWidth = newContainerWidth;
        this.pixelScaleRatio = newContainerWidth / this.maxScale;

        if (this.pixelScaleRatio <= 0) {
          this.pixelScaleRatio = 2;
        }




        this.updatePixelPositionsFromFixedScales(fixedScalePositions);

      } catch (error) {
        console.error('重新计算像素位置时出错:', error);
        this.recoverFromError();
      }
    },


    saveFixedScalePositions() {
      const positions = [];
      this.warehouseRows.forEach(row => {
        row.blocks.forEach(block => {
          try {

            let startScale, endScale;

            if (block.position && block.position.includes('-')) {
              const parts = block.position.split('-');
              endScale = parseInt(parts[0]);
              startScale = parseInt(parts[1]);
            } else {

              const materialTotalLeft = block.planLeft + block.materialLeft;
              startScale = this.maxScale - Math.round(materialTotalLeft / this.pixelScaleRatio);
              endScale = startScale - Math.round(block.materialWidth / this.pixelScaleRatio);
            }


            if (isNaN(startScale) || isNaN(endScale) || startScale < 0 || endScale < 0) {
              console.warn(`块${block.id}的刻度值无效:`, { startScale, endScale });
              return;
            }


            const materialLeftRatio = block.planWidth > 0 ? block.materialLeft / block.planWidth : 0;
            const materialWidthRatio = block.planWidth > 0 ? block.materialWidth / block.planWidth : 0;

            positions.push({
              blockId: block.id,
              materialStartScale: startScale,
              materialEndScale: endScale,
              materialLeftRatio: materialLeftRatio,
              materialWidthRatio: materialWidthRatio
            });



          } catch (error) {
            console.error(`保存块${block.id}的固定刻度时出错:`, error);
          }
        });
      });

      return positions;
    },


    updatePixelPositionsFromFixedScales(fixedPositions) {
      if (!fixedPositions || fixedPositions.length === 0) {
        console.warn('没有固定刻度数据进行更新');
        return;
      }


      this.warehouseRows.forEach(row => {
        row.blocks.forEach(block => {
          const position = fixedPositions.find(p => p.blockId === block.id);
          if (!position) {
            console.warn(`找不到块${block.id}的固定刻度数据`);
            return;
          }

          try {

            const materialStartPixel = (this.maxScale - position.materialStartScale) * this.pixelScaleRatio;
            const materialEndPixel = (this.maxScale - position.materialEndScale) * this.pixelScaleRatio;
            const materialWidthPixel = materialStartPixel - materialEndPixel;


            const planWidthPixel = position.materialWidthRatio > 0 ?
              materialWidthPixel / position.materialWidthRatio : materialWidthPixel;
            const materialLeftPixel = planWidthPixel * position.materialLeftRatio;
            const planLeftPixel = materialStartPixel - materialLeftPixel;




            const finalPlanWidth = Math.max(50, planWidthPixel);
            const finalPlanLeft = Math.max(0, Math.min(planLeftPixel, this.containerWidth - finalPlanWidth));
            const finalMaterialWidth = Math.max(30, materialWidthPixel);
            const finalMaterialLeft = Math.max(0, Math.min(materialLeftPixel, finalPlanWidth - finalMaterialWidth));


            this.$set(block, 'planLeft', finalPlanLeft);
            this.$set(block, 'planWidth', finalPlanWidth);
            this.$set(block, 'materialLeft', finalMaterialLeft);
            this.$set(block, 'materialWidth', finalMaterialWidth);


          } catch (error) {
            console.error(`更新块${block.id}像素位置时出错:`, error);
          }
        });
      });


    },


    recoverFromError() {


      setTimeout(() => {
        this.initializeContainer();
      }, 200);
    },


    validateScaleConsistency() {

      this.warehouseRows.forEach(row => {
        row.blocks.forEach(block => {
          if (block.position && block.position.includes('-')) {
            const parts = block.position.split('-');
            const positionStart = parseInt(parts[1]);
            const positionEnd = parseInt(parts[0]);


            const materialTotalLeft = block.planLeft + block.materialLeft;
            const calculatedStart = this.maxScale - Math.round(materialTotalLeft / this.pixelScaleRatio);
            const calculatedEnd = calculatedStart - Math.round(block.materialWidth / this.pixelScaleRatio);

            const startDiff = Math.abs(positionStart - calculatedStart);
            const endDiff = Math.abs(positionEnd - calculatedEnd);


          }
        });
      });

    },




    validatePositionCalculation() {
      console.log('=== 验证位置计算 ===');
      console.log('容器宽度:', this.containerWidth);
      console.log('像素比例:', this.pixelScaleRatio);
      console.log('最大刻度:', this.maxScale);


      const testPositions = [
        { start: 400, end: 700, name: '混匀料堆B' },
        { start: 50, end: 350, name: '混匀料堆A' },
        { start: 590, end: 598, name: '外购焦丁' }
      ];

      testPositions.forEach(pos => {
        const pixelPos = this.convertScaleToPixel(pos.start, pos.end, pos.end - pos.start);
        const rightEdge = pixelPos.planLeft + pixelPos.planWidth;
        const isOverflow = rightEdge > this.containerWidth;

        // console.log(`${pos.name} (${pos.start}-${pos.end}):`, {
        //   像素位置: pixelPos,
        //   右边界: rightEdge,
        //   容器宽度: this.containerWidth,
        //   是否超出: isOverflow
        // });
      });

      console.log('=== 验证完成 ===');
    },

    saveBlocksScalePositions() {
      const positions = [];
      this.warehouseRows.forEach(row => {
        row.blocks.forEach(block => {
          try {

            if (!this.pixelScaleRatio || this.pixelScaleRatio === 0) {
              console.warn('像素比例为0，跳过保存位置');
              return;
            }


            const planStartScale = this.maxScale - (block.planLeft / this.pixelScaleRatio);
            const planEndScale = planStartScale - (block.planWidth / this.pixelScaleRatio);

            if (isNaN(planStartScale) || isNaN(planEndScale)) {
              console.warn('检测到无效的刻度值:', {
                blockId: block.id,
                planLeft: block.planLeft,
                planWidth: block.planWidth,
                pixelScaleRatio: this.pixelScaleRatio
              });
              return;
            }


            const materialLeftRatio = block.planWidth > 0 ? block.materialLeft / block.planWidth : 0;
            const materialWidthRatio = block.planWidth > 0 ? block.materialWidth / block.planWidth : 0;

            positions.push({
              blockId: block.id,
              planStartScale: planStartScale,
              planEndScale: planEndScale,
              planWidth: block.planWidth,
              materialLeftRatio: materialLeftRatio,
              materialWidthRatio: materialWidthRatio
            });
          } catch (error) {
            console.error(`保存块${block.id}位置时出错:`, error);
          }
        });
      });
      console.log('保存的位置数据:', positions);
      return positions;
    },

    updateBlocksFromScalePositions(positions) {
      if (!positions || positions.length === 0) {
        console.warn('没有有效的位置数据进行更新');
        return;
      }

      console.log('开始更新块位置，容器宽度:', this.containerWidth, '像素比例:', this.pixelScaleRatio);

      this.warehouseRows.forEach(row => {
        row.blocks.forEach(block => {
          const position = positions.find(p => p.blockId === block.id);
          if (!position) {
            console.warn(`找不到块${block.id}的位置数据`);
            return;
          }

          try {

            if (!this.pixelScaleRatio || this.pixelScaleRatio === 0) {
              console.error('像素比例无效，跳过更新');
              return;
            }


            const newPlanLeft = (this.maxScale - position.planStartScale) * this.pixelScaleRatio;
            const newPlanWidth = (position.planStartScale - position.planEndScale) * this.pixelScaleRatio;
            const newMaterialLeft = newPlanWidth * position.materialLeftRatio;
            const newMaterialWidth = newPlanWidth * position.materialWidthRatio;

            console.log(`块${block.id}重新计算:`, {
              oldPos: { planLeft: block.planLeft, planWidth: block.planWidth },
              newPos: { planLeft: newPlanLeft, planWidth: newPlanWidth },
              scalePos: { start: position.planStartScale, end: position.planEndScale }
            });

            if ([newPlanLeft, newPlanWidth, newMaterialLeft, newMaterialWidth].some(isNaN)) {
              console.warn(`块${block.id}计算出无效值:`, {
                newPlanLeft, newPlanWidth, newMaterialLeft, newMaterialWidth
              });
              return;
            }


            const finalPlanWidth = Math.max(50, newPlanWidth);
            const finalMaterialWidth = Math.max(30, newMaterialWidth);


            const maxPlanLeft = Math.max(0, this.containerWidth - finalPlanWidth);
            const finalPlanLeft = Math.max(0, Math.min(newPlanLeft, maxPlanLeft));
            const finalMaterialLeft = Math.max(0, Math.min(newMaterialLeft, finalPlanWidth - finalMaterialWidth));


            this.$set(block, 'planLeft', finalPlanLeft);
            this.$set(block, 'planWidth', finalPlanWidth);
            this.$set(block, 'materialLeft', finalMaterialLeft);
            this.$set(block, 'materialWidth', finalMaterialWidth);


            const startScale = Math.round(position.planStartScale);
            const endScale = Math.round(position.planEndScale);
            if (!isNaN(startScale) && !isNaN(endScale)) {

              block.position = `${endScale}-${startScale}`;
            }

          } catch (error) {
            console.error(`更新块${block.id}位置时出错:`, error);
          }
        });
      });

      console.log('块位置更新完成');
    },
    initializeContainer() {
      const container = this.$el?.querySelector('.ruler-container');
      if (container) {
        this.containerWidth = container.clientWidth;


        if (this.containerWidth > 0) {
          this.pixelScaleRatio = this.containerWidth / this.maxScale;
        } else {
          this.pixelScaleRatio = 2;
        }

        if (this.pixelScaleRatio > 0) {
          this.updateAllBlockPositions();
          this.generateMaterialList();
        } else {
          setTimeout(() => {
            this.initializeContainer();
          }, 100);
        }
      } else {
        setTimeout(() => {
          this.initializeContainer();
        }, 100);
      }
    },
    updateAllBlockPositions() {

      if (!this.pixelScaleRatio) {

        return;
      }

      this.warehouseRows.forEach((row, rowIndex) => {
        row.blocks.forEach((block, blockIndex) => {
          try {

            const oldPos = {
              planLeft: block.planLeft,
              planWidth: block.planWidth,
              materialLeft: block.materialLeft,
              materialWidth: block.materialWidth
            };


            const newPos = this.calculateNewPosition(block);


            this.$set(block, 'planLeft', newPos.planLeft);
            this.$set(block, 'planWidth', newPos.planWidth);
            this.$set(block, 'materialLeft', newPos.materialLeft);
            this.$set(block, 'materialWidth', newPos.materialWidth);

          } catch (error) {
            // 更新块位置时出错，静默处理
          }
        });
      });


      this.$forceUpdate();
    },
    calculateNewPosition(block) {

      if (block.position && block.position.includes('-')) {
        const parts = block.position.split('-');
        const endPosition = parseInt(parts[0]);
        const startPosition = parseInt(parts[1]);

        if (isNaN(endPosition) || isNaN(startPosition)) {
          return this.getDefaultPosition();
        }

        if (!this.pixelScaleRatio || this.pixelScaleRatio <= 0) {
          return this.getDefaultPosition();
        }


        const newPlanLeft = (this.maxScale - endPosition) * this.pixelScaleRatio;
        const newPlanWidth = (endPosition - startPosition) * this.pixelScaleRatio;


        const materialLeftRatio = (block.planWidth && block.planWidth > 0) ? (block.materialLeft || 0) / block.planWidth : 0;
        const materialWidthRatio = (block.planWidth && block.planWidth > 0) ? (block.materialWidth || 0) / block.planWidth : 1;


        const newMaterialLeft = newPlanWidth * materialLeftRatio;
        const newMaterialWidth = newPlanWidth * materialWidthRatio;

        return {
          planLeft: Math.max(0, newPlanLeft),
          planWidth: Math.max(50, newPlanWidth),
          materialLeft: Math.max(0, newMaterialLeft),
          materialWidth: Math.max(30, newMaterialWidth)
        };
      }


      const planEndScale = this.maxScale - (block.planLeft / this.pixelScaleRatio);
      const planStartScale = this.maxScale - ((block.planLeft + block.planWidth) / this.pixelScaleRatio);

      const newPlanLeft = (this.maxScale - planEndScale) * this.pixelScaleRatio;
      const newPlanWidth = (planEndScale - planStartScale) * this.pixelScaleRatio;

      const materialLeftRatio = block.planWidth > 0 ? block.materialLeft / block.planWidth : 0;
      const materialWidthRatio = block.planWidth > 0 ? block.materialWidth / block.planWidth : 1;

      const newMaterialLeft = newPlanWidth * materialLeftRatio;
      const newMaterialWidth = newPlanWidth * materialWidthRatio;

      if (isNaN(newPlanLeft) || isNaN(newPlanWidth) || isNaN(newMaterialLeft) || isNaN(newMaterialWidth)) {
        return this.getDefaultPosition();
      }

      return {
        planLeft: Math.max(0, newPlanLeft),
        planWidth: Math.max(50, newPlanWidth),
        materialLeft: Math.max(0, newMaterialLeft),
        materialWidth: Math.max(30, newMaterialWidth)
      };
    },


    getDefaultPosition() {
      return {
        planLeft: 50,
        planWidth: 150,
        materialLeft: 0,
        materialWidth: 150
      };
    },

    validateInitialization() {
      const validations = [
        { name: 'pixelScaleRatio', value: this.pixelScaleRatio, valid: this.pixelScaleRatio > 0 },
        { name: 'containerWidth', value: this.containerWidth, valid: this.containerWidth >= 0 },
        { name: 'maxScale', value: this.maxScale, valid: this.maxScale > 0 },
        { name: 'warehouseRows', value: this.warehouseRows.length, valid: this.warehouseRows.length > 0 }
      ];

      let hasErrors = false;
      validations.forEach(validation => {
        if (!validation.valid) {
          hasErrors = true;
        }
      });

      let nanBlocks = [];
      this.warehouseRows.forEach(row => {
        row.blocks.forEach(block => {
          const hasNaN = [
            block.planLeft, block.planWidth,
            block.materialLeft, block.materialWidth
          ].some(val => isNaN(val));

          if (hasNaN) {
            nanBlocks.push({
              rowId: row.id,
              blockId: block.id
            });
          }
        });
      });

      if (nanBlocks.length > 0) {
        hasErrors = true;
      }

      if (hasErrors) {
        this.$message.warning('数据初始化存在问题，正在自动修复...');
        this.fixNaNValues();
      }
    },

    fixNaNValues() {
      let fixedCount = 0;

      if (!this.pixelScaleRatio || this.pixelScaleRatio <= 0 || isNaN(this.pixelScaleRatio)) {
        this.pixelScaleRatio = 2;
        fixedCount++;
      }

      this.warehouseRows.forEach(row => {
        row.blocks.forEach(block => {
          const defaultPos = this.getDefaultPosition();
          let blockFixed = false;

          ['planLeft', 'planWidth', 'materialLeft', 'materialWidth'].forEach(prop => {
            if (isNaN(block[prop]) || block[prop] === undefined || block[prop] === null) {
              this.$set(block, prop, defaultPos[prop]);
              blockFixed = true;
            }
          });

          if (blockFixed) {
            fixedCount++;
          }
        });
      });

      if (fixedCount > 0) {
        this.$message.success(`已自动修复 ${fixedCount} 个数据问题`);
        this.generateMaterialList();
      }
    },
    generateMaterialList() {
      const materials = [];
      this.warehouseRows.forEach(row => {
        row.blocks.forEach(block => {
          const position = this.calculateBlockPosition(block);
          materials.push({
            name: block.materialName,
            startPosition: position.start,
            endPosition: position.end,
            weight: block.materialWeight,
            id: block.id
          });
        });
      });
      this.materialList = materials;
    },
    calculateBlockPosition(block) {

      if (block.position && block.position.includes('-')) {
        const parts = block.position.split('-');
        return {
          start: parseInt(parts[1]),
          end: parseInt(parts[0])
        };
      }


      const start = this.getScalePosition(block.planLeft);
      const end = this.getScalePosition(block.planLeft + block.planWidth);


      block.position = `${end}-${start}`;

      return { start, end };
    },
    getScalePosition(pixelLeft) {
      if (typeof pixelLeft !== 'number' || isNaN(pixelLeft)) {
        return 0;
      }

      if (!this.pixelScaleRatio || this.pixelScaleRatio <= 0) {
        return 0;
      }

      const scale = this.maxScale - Math.round(pixelLeft / this.pixelScaleRatio);

      if (isNaN(scale)) {
        return 0;
      }

      return Math.max(0, Math.min(this.maxScale, scale));
    },

    async refreshData(isAutoRefresh = false, isPanelToggle = false) {
      try {
        this.loading = true;
        this.error = null;

        // 如果不是自动刷新（即手动刷新或切换页面），清空旧数据避免闪烁
        // 面板切换时也清空数据，确保重新计算布局
        if (!isAutoRefresh) {
          this.warehouseRows = [];
        }

        const id = this.stockyardId;
        let response;

        if (id === 'stockyard1') {
          response = await getStorehouseMapData_1();
        } else if (id === 'stockyard2') {
          response = await getStorehouseMapData_2();
        } 

        if (response && response.data && response.code === 200) {
          this.updateWarehouseDataFromAPI(response.data);

          // 只有手动刷新时才显示成功消息，面板切换时不显示
          if (!isAutoRefresh && !isPanelToggle) {
            this.$message.success('数据刷新成功');
          }
        } else {
          throw new Error(response.msg || '接口返回数据异常');
        }
      } catch (error) {
        this.error = '数据刷新失败';

        // 只有手动刷新时才显示错误消息，面板切换时不显示
        if (!isAutoRefresh && !isPanelToggle) {
          this.$message.error('数据刷新失败: ' + (error.message || '未知错误'));
        }
      } finally {
        this.loading = false;
      }
    },

    /**
     * 从后端API数据(planList/realList)更新 warehouseRows 结构
     * - 后端返回的数据对象，包含 planList 和 realList
     * 调用转换函数，将后端数据转为前端渲染用的 warehouseRows，并初始化容器
     */
    updateWarehouseDataFromAPI(apiData) {
      try {

        if (!apiData.planList || !apiData.realList) {
          console.warn('API数据格式不正确，缺少planList或realList');
          return;
        }

        // 转换API数据为组件数据格式

        const newWarehouseRows = this.transformAPIDataToWarehouseRows(apiData);


        if (newWarehouseRows && newWarehouseRows.length > 0) {
          //console.log('更新组件数据，新数据行数:', newWarehouseRows.length);


          const oldRowsCount = this.warehouseRows.length;
          // console.log('旧数据行数:', oldRowsCount);


          this.warehouseRows = newWarehouseRows;
          //console.log('数据已更新，当前warehouseRows:', this.warehouseRows);


          this.$nextTick(() => {
            this.initializeContainer();

          });
        } else {
          console.warn('转换后的数据为空，保持原有数据');
        }
      } catch (error) {
        console.error('更新API数据时出错:', error);
        throw error;
      }
    },

    /**
     * 将后端 planList/realList 转换为 warehouseRows 结构

     * warehouseRows - 前端渲染用的嵌套结构数组

     */
    transformAPIDataToWarehouseRows(apiData) {
      try {


        const { planList, realList } = apiData;

        // 按料条分组
        const groupedData = this.groupDataByCrossRegion(planList, realList);

        // 转换为warehouseRows格式
        const warehouseRows = [];


        const regionMapping = {
          '1': 'A',
          '2': 'B',
          '3': 'C',
          '4': 'D',
          '5': 'E'
        };

        Object.keys(groupedData).forEach(crossRegion => {
          const label = regionMapping[crossRegion] || crossRegion;
          const regionData = groupedData[crossRegion];

          const row = {
            id: parseInt(crossRegion),
            label: label,
            blocks: this.createBlocksFromRegionData(regionData, crossRegion)
          };

          warehouseRows.push(row);
        });

        // 按标签排序 (从下到上: A, B, C, D, E) ，但在数组中的顺序是从上到下: E, D, C, B, A
        warehouseRows.sort((a, b) => b.label.localeCompare(a.label));

        //  console.log('转换完成的仓库数据:', warehouseRows);
        return warehouseRows;

      } catch (error) {
        console.error('转换API数据时出错:', error);
        return [];
      }
    },


    groupDataByCrossRegion(planList, realList) {
      const grouped = {};

      // 处理计划数据
      planList.forEach(plan => {
        const region = plan.crossRegion;
        if (!grouped[region]) {
          grouped[region] = { plans: [], reals: [] };
        }
        grouped[region].plans.push(plan);
      });

      // 处理实际数据
      realList.forEach(real => {
        const region = real.crossRegion;
        if (!grouped[region]) {
          grouped[region] = { plans: [], reals: [] };
        }
        grouped[region].reals.push(real);
      });

      return grouped;
    },


    createBlocksFromRegionData(regionData, crossRegion) {
      const blocks = [];
      const { plans, reals } = regionData;

      // 按垛位分组
      const stackingGroups = {};

      // 处理计划数据
      plans.forEach(plan => {
        const stackingPosition = plan.stackingPosition;
        if (!stackingGroups[stackingPosition]) {
          stackingGroups[stackingPosition] = { plan: null, real: null };
        }
        stackingGroups[stackingPosition].plan = plan;
      });

      // 处理实际数据
      reals.forEach(real => {
        const stackingPosition = real.stackingPosition;
        if (!stackingGroups[stackingPosition]) {
          stackingGroups[stackingPosition] = { plan: null, real: null };
        }
        stackingGroups[stackingPosition].real = real;
      });

      // 创建块数据
      Object.keys(stackingGroups).forEach(stackingPosition => {
        const group = stackingGroups[stackingPosition];
        const block = this.createBlockFromGroup(group, crossRegion, stackingPosition);
        if (block) {
          blocks.push(block);
        }
      });

      // 按垛位排序
      blocks.sort((a, b) => {
        const aPos = a.stackingPosition.split('-')[1];
        const bPos = b.stackingPosition.split('-')[1];
        return parseInt(aPos) - parseInt(bPos);
      });

      return blocks;
    },


    createBlockFromGroup(group, crossRegion, stackingPosition) {
      const { plan, real } = group;

      if (!plan && !real) {
        return null;
      }

      // 拼接ID
      const blockId = `${crossRegion}-${stackingPosition}`;

      // 计划块信息
      const planStartPosition = plan ? plan.startPosition : 0;
      const planEndPosition = plan ? plan.endPosition : 0;
      const planStackLength = plan ? plan.stackLength : Math.abs(planStartPosition - planEndPosition);

      // 转换计划块为像素位置
      const planPixelPositions = this.convertScaleToPixel(planStartPosition, planEndPosition, planStackLength);

      // 物料块信息-物料位置为null，充满计划块
      let materialPixelPositions;
      if (real && real.startPosition !== null && real.endPosition !== null) {
        //  修复物料位置计算
        const realStackLength = Math.abs(real.startPosition - real.endPosition);
        const realAbsolutePositions = this.convertScaleToPixel(real.startPosition, real.endPosition, realStackLength);


        materialPixelPositions = {
          materialLeft: Math.max(0, realAbsolutePositions.planLeft - planPixelPositions.planLeft),
          materialWidth: realAbsolutePositions.planWidth
        };

        // console.log(`物料位置计算: real(${real.startPosition}-${real.endPosition}) -> materialLeft=${materialPixelPositions.materialLeft}, materialWidth=${materialPixelPositions.materialWidth}`);
      } else {
        // 没有实际物料时，充满整个计划块
        materialPixelPositions = {
          materialLeft: 0, // 相对于计划块的左边距为0
          materialWidth: planPixelPositions.planWidth // 宽度等于计划块宽度
        };
      }

      const block = {
        id: blockId,
        stackingPosition: stackingPosition,


        stockRealId: real ? real.stockRealId : null,  // 实际库存ID
        stackPlanId: plan ? plan.stackPlanId : null,  // 计划ID

        // 计划块信息
        planName: plan ? plan.stackName : '未命名', // 计划块名称
        planWeight: plan ? (plan.planWeight || 0) : 0,
        planLeft: planPixelPositions.planLeft,
        planWidth: planPixelPositions.planWidth,

        // 实际物料信息
        materialName: real ? real.mateName : '无物料', // 实际物料名称
        materialWeight: real ? (real.stockWeight || 0) : 0,
        materialLeft: materialPixelPositions.materialLeft,
        materialWidth: materialPixelPositions.materialWidth,

        // 位置信息
        position: `${planEndPosition}-${planStartPosition}`,
        startPosition: planStartPosition,
        endPosition: planEndPosition,
        stackLength: planStackLength,

        // 原始数据引用
        planData: plan,
        realData: real
      };

      return block;
    },

    /**刻度区间转为像素区间（右大左小）*/
    convertScaleToPixel(startPosition, endPosition, stackLength) {

      if ((!startPosition && startPosition !== 0) && (!endPosition && endPosition !== 0) && !stackLength) {
        return {
          planLeft: 50,
          planWidth: 150,
          materialLeft: 0,
          materialWidth: 150
        };
      }


      const ratio = this.pixelScaleRatio || 2;



      const planLeft = (this.maxScale - endPosition) * ratio;
      const planWidth = (endPosition - startPosition) * ratio;

      // console.log(`计算结果: planLeft=${planLeft}, planWidth=${planWidth}`);

      return {
        planLeft: Math.max(0, planLeft),
        planWidth: Math.max(50, planWidth)
      };
    },

    handleBlockHover(materialName) {
      this.hoveredMaterial = materialName;
    },
    handleBlockLeave() {
      this.hoveredMaterial = null;
    },
    handleItemHover(materialName) {
      this.hoveredMaterial = materialName;
    },
    handleItemLeave() {
      this.hoveredMaterial = null;
    },
    isHighlighted(materialName) {
      return this.hoveredMaterial === materialName;
    },
    checkOverlap(currentBlock, newLeft, newWidth) {
      const currentStripBlocks = this.warehouseRows
        .find(row => row.blocks.includes(currentBlock))
        .blocks;

      return currentStripBlocks.some(block => {
        if (block === currentBlock) return false;

        const block1Left = newLeft;
        const block1Right = newLeft + newWidth;
        const block2Left = block.planLeft;
        const block2Right = block.planLeft + block.planWidth;

        return !(block1Right <= block2Left || block1Left >= block2Right);
      });
    },

    calculateDragPosition(clientX, block, type = 'plan') {
      const container = this.$el.querySelector('.ruler-container');
      if (!container) return null;

      const containerRect = container.getBoundingClientRect();
      const deltaX = clientX - this.dragState.startX;

      let newLeft, newWidth;
      const minScaleWidth = this.pixelScaleRatio; // 1刻度对应的像素宽度

      if (type === 'material') {
        // 料块的拖拽和调整大小
        if (this.dragState.isResizing) {
          if (this.dragState.resizeDirection === 'left') {
            newWidth = this.dragState.startWidth - deltaX;
            newLeft = this.dragState.startLeft + deltaX;

            // 最小宽度为1刻度
            if (newWidth < minScaleWidth) {
              newWidth = minScaleWidth;
              newLeft = this.dragState.startLeft + this.dragState.startWidth - minScaleWidth;
            }

            // 不能超出计划块
            if (newLeft < 0) {
              newLeft = 0;
              newWidth = this.dragState.startLeft + this.dragState.startWidth;
            }
          } else {
            newLeft = this.dragState.startLeft;
            newWidth = this.dragState.startWidth + deltaX;


            newWidth = Math.max(minScaleWidth, newWidth);
            newWidth = Math.min(newWidth, block.planWidth - newLeft);
          }
        } else {
          // 拖动
          newLeft = this.dragState.startLeft + deltaX;
          newWidth = this.dragState.startWidth;


          newLeft = Math.max(0, Math.min(newLeft, block.planWidth - newWidth));
        }
      } else {

        if (this.dragState.isResizing) {
          if (this.dragState.resizeDirection === 'left') {
            newWidth = this.dragState.startWidth - deltaX;
            newLeft = this.dragState.startLeft + deltaX;


            if (newWidth < minScaleWidth) {
              newWidth = minScaleWidth;
              newLeft = this.dragState.startLeft + this.dragState.startWidth - minScaleWidth;
            }


            if (newLeft < 0) {
              newLeft = 0;
              newWidth = this.dragState.startLeft + this.dragState.startWidth;
            }
          } else {
            newLeft = this.dragState.startLeft;
            newWidth = this.dragState.startWidth + deltaX;


            newWidth = Math.max(minScaleWidth, newWidth);


            const maxWidth = containerRect.width - newLeft;
            newWidth = Math.min(newWidth, maxWidth);
          }
        } else {

          newLeft = this.dragState.startLeft + deltaX;
          newWidth = this.dragState.startWidth;


          newLeft = Math.max(0, Math.min(newLeft, containerRect.width - newWidth));
        }
      }


      let scaleStart, scaleEnd;

      if (type === 'material') {

        const totalLeft = block.planLeft + newLeft;
        scaleStart = this.maxScale - (totalLeft / this.pixelScaleRatio);
        scaleEnd = scaleStart - (newWidth / this.pixelScaleRatio);
      } else {
        // 计划块的刻度值直接计算
        scaleStart = this.maxScale - (newLeft / this.pixelScaleRatio);
        scaleEnd = scaleStart - (newWidth / this.pixelScaleRatio);
      }

      return {
        left: newLeft,
        width: newWidth,
        scaleStart: Math.round(scaleStart),
        scaleEnd: Math.round(scaleEnd)
      };
    },


    updateDragState(event, block, type = 'plan') {
      if (!this.dragState.isDragging) return;

      const clientX = event.touches ? event.touches[0].clientX : event.clientX;
      const newPosition = this.calculateDragPosition(clientX, block, type);

      if (!newPosition) return;

      if (type === 'plan') {
        // 检查是否会与其他计划块重叠
        if (this.checkOverlap(block, newPosition.left, newPosition.width)) {
          return; // 如果会重叠，则不更新位置
        }

        // 更新计划块位置
        block.planLeft = newPosition.left;
        block.planWidth = newPosition.width;


        if (block.materialLeft + block.materialWidth > newPosition.width) {

          block.materialWidth = Math.max(this.pixelScaleRatio, newPosition.width - block.materialLeft);
        }

        // 更新位置信息
        const materialTotalLeft = newPosition.left + block.materialLeft;
        const materialScaleStart = this.maxScale - (materialTotalLeft / this.pixelScaleRatio);
        const materialScaleEnd = materialScaleStart - (block.materialWidth / this.pixelScaleRatio);
        // 保持与创建时一致的格式：endPosition-startPosition（大值-小值）
        block.position = `${Math.round(materialScaleStart)}-${Math.round(materialScaleEnd)}`;

        // 更新 startPosition 和 endPosition startPosition是小值，endPosition是大值
        block.startPosition = Math.round(materialScaleEnd);
        block.endPosition = Math.round(materialScaleStart);

        this.$forceUpdate();
      } else {

        block.materialLeft = newPosition.left;
        block.materialWidth = newPosition.width;


        const materialTotalLeft = block.planLeft + newPosition.left;
        const materialScaleStart = this.maxScale - (materialTotalLeft / this.pixelScaleRatio);
        const materialScaleEnd = materialScaleStart - (newPosition.width / this.pixelScaleRatio);
        // endPosition-startPosition（大值-小值）
        block.position = `${Math.round(materialScaleStart)}-${Math.round(materialScaleEnd)}`;

        // 更新 startPosition 和 endPosition  startPosition是小值，endPosition是大值
        block.startPosition = Math.round(materialScaleEnd);
        block.endPosition = Math.round(materialScaleStart);

        // 强制更新计算属性
        this.$forceUpdate();
      }
    },

    // 开始拖拽计划块
    startPlanDrag(event, block, rowLabel) {
      if (!this.isEditing) return;
      event.preventDefault();
      event.stopPropagation();


      this.hideUnifiedTooltip();

      const clientX = event.touches ? event.touches[0].clientX : event.clientX;

      this.dragState = {
        isDragging: true,
        isResizing: false,
        currentBlock: block,
        startX: clientX,
        startLeft: block.planLeft,
        startWidth: block.planWidth,
        type: 'plan',
        rowLabel
      };

      document.addEventListener('mousemove', this.handlePlanDrag);
      document.addEventListener('mouseup', this.endDrag);
      document.addEventListener('touchmove', this.handlePlanDrag);
      document.addEventListener('touchend', this.endDrag);

      event.target.classList.add('dragging');
    },

    // 开始调整计划块大小
    startPlanResize(event, block, direction) {
      if (!this.isEditing) return;
      event.preventDefault();
      event.stopPropagation();

      // 隐藏悬浮提示
      this.hideUnifiedTooltip();

      const clientX = event.touches ? event.touches[0].clientX : event.clientX;

      this.dragState = {
        isDragging: true,
        isResizing: true,
        currentBlock: block,
        startX: clientX,
        startLeft: block.planLeft,
        startWidth: block.planWidth,
        resizeDirection: direction,
        type: 'plan'
      };

      document.addEventListener('mousemove', this.handlePlanDrag);
      document.addEventListener('mouseup', this.endDrag);
      document.addEventListener('touchmove', this.handlePlanDrag);
      document.addEventListener('touchend', this.endDrag);

      event.target.classList.add('resizing');
    },

    // 处理计划块拖拽（节流入口）
    handlePlanDrag(event) {
      event.preventDefault();

      this.dragPerformanceStats.totalDragEvents++;

      this.throttledPlanDrag(event);
    },


    handlePlanDragCore(event) {
      if (!this.dragState.isDragging || this.dragState.type !== 'plan') return;


      this.dragPerformanceStats.throttledDragEvents++;
      this.dragPerformanceStats.lastDragTime = Date.now();

      this.updateDragState(event, this.dragState.currentBlock, 'plan');
    },


    startMaterialDrag(event, block) {
      if (!this.isEditing) return;
      event.preventDefault();
      event.stopPropagation();


      this.hideUnifiedTooltip();

      // 记录触摸开始时间（用于长按检测）
      if (event.touches) {
        this.touchStartTime = Date.now();
      }

      const clientX = event.touches ? event.touches[0].clientX : event.clientX;

      this.dragState = {
        isDragging: true,
        isResizing: false,
        currentBlock: block,
        startX: clientX,
        startLeft: block.materialLeft,
        startWidth: block.materialWidth,
        type: 'material'
      };

      document.addEventListener('mousemove', this.handleMaterialDrag);
      document.addEventListener('mouseup', this.endDrag);
      document.addEventListener('touchmove', this.handleMaterialDrag);
      document.addEventListener('touchend', this.endDrag);

      event.target.classList.add('dragging');
    },

    // 开始调整物料块大小
    startMaterialResize(event, block, direction) {
      if (!this.isEditing) return;
      event.preventDefault();
      event.stopPropagation();

      // 隐藏悬浮提示
      this.hideUnifiedTooltip();

      const clientX = event.touches ? event.touches[0].clientX : event.clientX;

      this.dragState = {
        isDragging: true,
        isResizing: true,
        currentBlock: block,
        startX: clientX,
        startLeft: block.materialLeft,
        startWidth: block.materialWidth,
        resizeDirection: direction,
        type: 'material'
      };

      document.addEventListener('mousemove', this.handleMaterialDrag);
      document.addEventListener('mouseup', this.endDrag);
      document.addEventListener('touchmove', this.handleMaterialDrag);
      document.addEventListener('touchend', this.endDrag);

      event.target.classList.add('resizing');
    },

    // 处理物料块拖拽（节流）
    handleMaterialDrag(event) {
      event.preventDefault();

      this.dragPerformanceStats.totalDragEvents++;

      this.throttledMaterialDrag(event);
    },


    handleMaterialDragCore(event) {
      if (!this.dragState.isDragging || this.dragState.type !== 'material') return;


      this.dragPerformanceStats.throttledDragEvents++;
      this.dragPerformanceStats.lastDragTime = Date.now();

      this.updateDragState(event, this.dragState.currentBlock, 'material');
    },

    // 结束拖拽
    endDrag(event) {
      if (!this.dragState.isDragging) return;

      // 重置拖拽状态
      this.isDragging = false;


      this.cleanupDragListeners();
      // 强制更新计算属性
      this.$forceUpdate();
    },
    // 清理所有拖拽相关的事件监听器
    cleanupDragListeners() {
      // 移除所有拖拽相关的事件监听器
      document.removeEventListener('mousemove', this.handlePlanDrag);
      document.removeEventListener('mouseup', this.endDrag);
      document.removeEventListener('touchmove', this.handlePlanDrag);
      document.removeEventListener('touchend', this.endDrag);
      document.removeEventListener('mousemove', this.handleMaterialDrag);
      document.removeEventListener('touchmove', this.handleMaterialDrag);

      // 重置拖拽状态
      this.isDragging = false;
      this.dragState = {
        isDragging: false,
        currentBlock: null,
        startX: 0,
        startLeft: 0,
        isResizing: false,
        resizeDirection: null,
        type: null
      };

      // 重置样式
      document.body.style.userSelect = '';
      document.body.style.cursor = '';


      const draggingElements = document.querySelectorAll('.dragging, .resizing');
      draggingElements.forEach(el => {
        el.classList.remove('dragging', 'resizing');
      });
    },
    // 设置侧边栏监听器
    setupSidebarWatcher() {
      // 监听侧边栏状态变化
      if (this.$store && this.$store.state.app) {
        this.$watch(
          () => this.$store.state.app.sidebar.opened,
          (newVal, oldVal) => {
            if (newVal !== oldVal) {
              console.log('侧边栏状态变化:', oldVal, '->', newVal);
              // 延迟执行以确保DOM已更新，使用防抖处理
              setTimeout(() => {
                this.debouncedResize();
              }, 0);
            }
          }
        );
      }

      // DOM变化监听
      if (typeof MutationObserver !== 'undefined') {
        const targetNode = document.querySelector('.app-main') || document.body;
        const observer = new MutationObserver((mutations) => {
          let shouldResize = false;
          mutations.forEach((mutation) => {
            if (mutation.type === 'attributes' &&
              (mutation.attributeName === 'class' || mutation.attributeName === 'style')) {
              shouldResize = true;
            }
          });

          if (shouldResize) {
            // 使用防抖处理DOM变化引起的布局变化
            setTimeout(() => {
              this.debouncedContainerResize(); // 重新计算布局，不重新加载数据
            }, 350);
          }
        });

        observer.observe(targetNode, {
          attributes: true,
          attributeFilter: ['class', 'style'],
          subtree: true
        });

        // 保存observer以便清理
        this.mutationObserver = observer;
      }
    },

    // 防抖函数
    debounce(fn, delay) {
      let timer = null;
      return function () {
        if (timer) {
          clearTimeout(timer);
        }
        timer = setTimeout(() => {
          fn.apply(this, arguments);
        }, delay);
      }
    },


    // 只在变化超过10px阈值时被调用
    async handleWindowResize() {
      // 防止重复执行
      if (this.windowResizeInProgress) {

        return;
      }

      try {
        this.windowResizeInProgress = true;


        const oldIsMobile = this.isMobile;
        const oldIsTablet = this.isTablet;
        this.detectDeviceType();

        // 设备类型发生变化
        if (oldIsMobile !== this.isMobile || oldIsTablet !== this.isTablet) {
          console.log('设备类型变化:', {
            mobile: `${oldIsMobile} -> ${this.isMobile}`,
            tablet: `${oldIsTablet} -> ${this.isTablet}`
          });
        }

        // 移动端横屏检查
        if (this.isMobile) {
          this.checkOrientation();
        }

        this.loading = true;
        this.error = null;

        console.log('重新加载数据...');


        await this.initializeData();


        this.$nextTick(() => {
          this.handleContainerResize();
        });


      } catch (error) {

        this.error = '窗口大小变化处理失败';
        this.$message.error('页面布局更新失败，请刷新页面');
      } finally {
        this.loading = false;
        // 延迟重置标志
        setTimeout(() => {
          this.windowResizeInProgress = false;
        }, 200);
      }
    },

    // 手动触发窗口大小变化处理
    forceWindowResize() {

      // 重置状态
      this.windowResizeInProgress = false;
      // 强制触发检查（绕过阈值检查）
      const currentWidth = window.innerWidth;
      const currentHeight = window.innerHeight;

      // 立即执行处理
      this.handleWindowResize();
    },





    // 启动自动刷新
    startAutoRefresh() {

      if (this.autoRefreshTimer) {
        clearInterval(this.autoRefreshTimer);
      }



      this.autoRefreshTimer = setInterval(() => {

        if (!this.isEditing) {
          this.refreshData(true);
        } else {

        }
      }, this.autoRefreshInterval);
    },


    stopAutoRefresh() {
      if (this.autoRefreshTimer) {

        clearInterval(this.autoRefreshTimer);
        this.autoRefreshTimer = null;
      }
    },

    // 获取指定行的大机
    getStackersForRow(rowLabel) {
      const rowMap = {
        'E': ['DE'],
        'D': [],
        'C': ['CD'],
        'B': ['BC'],
        'A': ['AB']
      };

      const locations = rowMap[rowLabel] || [];
      const result = {};

      locations.forEach(location => {
        if (this.stackerReclaimers[location]) {
          result[location] = this.stackerReclaimers[location];
        }
      });

      return result;
    },

    // 获取相对于刻度尺的定位样式
    getStackerStyleForRuler(stacker, location) {
      const scale = stacker.position.scale;

      // 支持任意位置的插值计算
      let percentage;

      // 验证scale是否为有效数字，如果不是则使用默认值
      const validScale = (typeof scale === 'number' && !isNaN(scale)) ? scale : 350;

      // 限制刻度在有效范围内 (0-700)
      const clampedScale = Math.max(0, Math.min(700, validScale));

      // 直接根据刻度值计算百分比位置，支持任意数值
      // 刻度700对应0%，刻度0对应100%
      percentage = ((700 - clampedScale) / 700) * 100;


      const stackerPositions = {
        'DE': {
          top: '110px'
        },
        'CD': {
          top: '-55px'
        },
        'BC': {
          top: '-50px'
        },
        'AB': {
          top: '-50px'
        }
      };

      const positionConfig = stackerPositions[location] || stackerPositions['DE'];
      const topPosition = positionConfig.top;

      const style = {
        position: 'absolute',
        left: `${percentage}%`,
        top: topPosition,
        transform: 'translateX(-50%)',
        zIndex: 100
      };

      return style;
    },

    // 堆取料机相关方法

    // 获取大机移动层位置样式
    getStackerMobileStyle(stacker, location) {
      const scale = stacker.position.scale;

      //  支持任意位置的插值计算
      // 限制刻度在有效范围内 (0-700)
      const clampedScale = Math.max(0, Math.min(700, scale));

      // 直接根据刻度值计算百分比位置，支持任意数值
      // 刻度700对应0%，刻度0对应100%
      const percentage = ((700 - clampedScale) / 700) * 100;

      // row-label的偏移（30px宽度 + 10px margin-right）
      const rowLabelOffset = 40; // 30px + 10px

      const leftPosition = `calc(${rowLabelOffset}px + (100% - ${rowLabelOffset}px) * ${percentage / 100})`;

      // 根据location计算垂直位置（EDCBA布局）
      const rowHeight = 120;
      const locationMap = {
        'DE': 0, // E行(0)和D行(1)之间
        'CD': 1, // D行(1)和C行(2)之间
        'BC': 2, // C行(2)和B行(3)之间
        'AB': 3  // B行(3)和A行(4)之间
      };

      const topPosition = (locationMap[location] + 1) * rowHeight - 10;

      return {
        position: 'absolute',
        left: leftPosition,
        top: `${topPosition}px`,
        transform: 'translateX(-50%)', // 机房中心对准刻度线
        transition: 'left 2s ease-in-out', // 平滑移动动画
        zIndex: 500 // 最高层级
      };
    },

    // 堆取料机点击事件
    handleStackerClick(stacker) {
      const scaleNum = stacker.position.scale;
      console.log('点击堆取料机，当前位置:', scaleNum);

      this.$message.info(`堆取料机当前在刻度 ${scaleNum} 位置 - 状态: ${this.getStackerStatusText(stacker.equipment.status)}`);
    },

    // 移动指定大机到指定刻度
    moveStackerToScale(location, targetScale) {
      console.log('移动大机到刻度:', location, targetScale);

      const stacker = this.stackerReclaimers[location];
      if (!stacker) return;


      stacker.equipment.status = 'moving';
      stacker.equipment.boomAngle = 0;
      stacker.equipment.bucketWheelSpeed = 0;
      stacker.operation.mode = 'idle';


      stacker.position.scale = targetScale;


      setTimeout(() => {
        stacker.equipment.status = 'working';
        stacker.equipment.boomAngle = 45;
        stacker.equipment.bucketWheelSpeed = 120;
        stacker.operation.mode = 'reclaiming';
      }, 2000);

      this.$message.info(`${stacker.name}移动到刻度 ${targetScale} 位置`);
    },


    moveStackerToLocation(stackerId, newLocation) {
      console.log('移动大机到位置:', stackerId, newLocation);


      const stacker = Object.values(this.stackerReclaimers).find(s => s.id === stackerId);
      if (!stacker) return;


      stacker.equipment.status = 'moving';
      stacker.equipment.boomAngle = 0;
      stacker.equipment.bucketWheelSpeed = 0;
      stacker.operation.mode = 'idle';


      stacker.position.location = newLocation;


      setTimeout(() => {
        stacker.equipment.status = 'working';
        stacker.equipment.boomAngle = 45;
        stacker.equipment.bucketWheelSpeed = 120;
        stacker.operation.mode = 'reclaiming';
      }, 2000);

      const locationNames = {
        'AB': 'A-B料条间',
        'BC': 'B-C料条间',
        'CD': 'C-D料条间',
        'DE': 'D-E料条间'
      };

      this.$message.info(`${stacker.name}移动到${locationNames[newLocation] || newLocation}位置`);
    },


    moveAllStackersToScale(targetScale) {
      console.log('批量移动所有大机到刻度:', targetScale);
      Object.keys(this.stackerReclaimers).forEach(location => {
        this.moveStackerToScale(location, targetScale);
      });
    },


    findNearestStacker(targetScale) {
      let nearestStacker = null;
      let minDistance = Infinity;

      Object.entries(this.stackerReclaimers).forEach(([location, stacker]) => {
        const distance = Math.abs(stacker.position.scale - targetScale);
        if (distance < minDistance) {
          minDistance = distance;
          nearestStacker = { location, stacker };
        }
      });

      return nearestStacker;
    },


    onScaleClick(scale) {
      console.log('点击刻度线:', scale);

      const nearest = this.findNearestStacker(parseInt(scale));
      if (nearest) {
        this.moveStackerToScale(nearest.location, parseInt(scale));
      }
    },


    handleStackerHover(stacker) {
      const scaleNum = stacker.position.scale;
      console.log('悬停堆取料机，刻度位置:', scaleNum);


      if (stacker.operation.targetMaterial && stacker.equipment.status === 'working') {
        this.hoveredMaterial = stacker.operation.targetMaterial;
      }
    },


    handleStackerLeave(stacker) {

      this.hoveredMaterial = '';
    },

    // 获取堆取料机状态文本
    getStackerStatusText(status) {
      const statusMap = {
        'idle': '待机',
        'working': '作业中',
        'moving': '移动中',
        'maintenance': '维护'
      };
      return statusMap[status] || '未知';
    },







    ensureStackerDataTypes() {


      const stackerIds = ['AB', 'BC', 'CD', 'DE'];
      const defaultValues = {
        'AB': { scale: 350, boomAngle: 0, bucketWheelSpeed: 0 },
        'BC': { scale: 450, boomAngle: 30, bucketWheelSpeed: 120 },
        'CD': { scale: 500, boomAngle: -15, bucketWheelSpeed: 0 },
        'DE': { scale: 600, boomAngle: 45, bucketWheelSpeed: 120 }
      };

      stackerIds.forEach(id => {
        const stacker = this.stackerReclaimers[id];
        const defaults = defaultValues[id];


        if (isNaN(stacker.position.scale) || stacker.position.scale === null || stacker.position.scale === undefined) {
          console.warn(`修复${id}大机scale: ${stacker.position.scale} → ${defaults.scale}`);
          stacker.position.scale = defaults.scale;
        } else {
          stacker.position.scale = Number(stacker.position.scale);
        }


        if (isNaN(stacker.equipment.boomAngle) || stacker.equipment.boomAngle === null || stacker.equipment.boomAngle === undefined) {
          console.warn(`修复${id}大机boomAngle: ${stacker.equipment.boomAngle} → ${defaults.boomAngle}`);
          stacker.equipment.boomAngle = defaults.boomAngle;
        } else {
          stacker.equipment.boomAngle = Number(stacker.equipment.boomAngle);
        }


        if (isNaN(stacker.equipment.bucketWheelSpeed) || stacker.equipment.bucketWheelSpeed === null || stacker.equipment.bucketWheelSpeed === undefined) {
          console.warn(`修复${id}大机bucketWheelSpeed: ${stacker.equipment.bucketWheelSpeed} → ${defaults.bucketWheelSpeed}`);
          stacker.equipment.bucketWheelSpeed = defaults.bucketWheelSpeed;
        } else {
          stacker.equipment.bucketWheelSpeed = Number(stacker.equipment.bucketWheelSpeed);
        }


        if (stacker.position.targetScale !== undefined) {
          stacker.position.targetScale = Number(stacker.position.targetScale) || stacker.position.scale;
        }
        if (stacker.equipment.targetBoomAngle !== undefined) {
          stacker.equipment.targetBoomAngle = Number(stacker.equipment.targetBoomAngle) || stacker.equipment.boomAngle;
        }
        if (stacker.equipment.targetBucketWheelSpeed !== undefined) {
          stacker.equipment.targetBucketWheelSpeed = Number(stacker.equipment.targetBucketWheelSpeed) || stacker.equipment.bucketWheelSpeed;
        }
      });
    },

    // 块悬停处理
    handleUnifiedBlockHover(event, block, type) {
      // 如果正在拖拽，不显示悬浮提示
      if (this.isDragging) {
        return;
      }

      const blockId = this.generateBlockId(block);

      if (this.unifiedTooltip.visible && this.unifiedTooltip.currentBlockId === blockId) {
        return;
      }

      this.handleBlockHover(block.materialName);

      this.showUnifiedTooltip(event, block, type, blockId);
    },

    handleUnifiedBlockLeave() {

      this.handleBlockLeave();

      // 隐藏统一提示
      this.hideUnifiedTooltip();
    },


    generateBlockId(block) {

      const planLeft = block.planLeft || 0;
      const planWidth = block.planWidth || 0;
      const materialName = block.materialName || '';
      const planName = block.planName || '';

      return `${planLeft}_${planWidth}_${materialName}_${planName}`;
    },



    handleMouseDown(event, block, rowLabel, type) {

      if (!this.isEditing) {
        return;
      }


      this.isDragging = true;



      this.hideUnifiedTooltip();


      if (type === 'plan') {
        this.startPlanDrag(event, block, rowLabel);
      } else if (type === 'material') {
        this.startMaterialDrag(event, block);
      }
    },


    handleTouchStart(event, block, rowLabel, type) {

      if (!this.isEditing) {
        return;
      }

      this.isDragging = true;

      this.hideUnifiedTooltip();

      if (type === 'plan') {
        this.startPlanDrag(event, block, rowLabel);
      } else if (type === 'material') {
        this.startMaterialDrag(event, block);
      }
    },

    handleResizeMouseDown(event, block, direction, type) {

      if (!this.isEditing) {
        return;
      }


      this.isDragging = true;


      this.hideUnifiedTooltip();

      if (type === 'plan') {
        this.startPlanResize(event, block, direction);
      } else if (type === 'material') {
        this.startMaterialResize(event, block, direction);
      }
    },


    handleResizeTouchStart(event, block, direction, type) {

      if (!this.isEditing) {
        return;
      }

      // 设置拖拽状态
      this.isDragging = true;

      // 立即隐藏悬浮提示
      this.hideUnifiedTooltip();


      if (type === 'plan') {
        this.startPlanResize(event, block, direction);
      } else if (type === 'material') {
        this.startMaterialResize(event, block, direction);
      }
    },

    // 显示统一悬浮提示
    showUnifiedTooltip(event, block, primaryType, blockId) {

      if (this.isDragging) {
        return;
      }

      const rect = event.target.getBoundingClientRect();

      // 计划数据
      const planData = {
        name: block.planName || block.materialName || '未知计划',
        weight: block.planWeight || 0
      };

      // 物料数据
      let materialData = null;
      if (this.hasMaterialBlock(block)) {
        materialData = {
          name: block.materialName || '未知物料',
          weight: block.materialWeight || 0,
          position: block.position || '-'
        };
      }

      // 设置统一提示数据
      this.unifiedTooltip = {
        visible: true,
        x: rect.left + rect.width / 2,
        y: rect.top - 10,
        planData: planData,
        materialData: materialData,
        currentBlockId: blockId
      };
    },

    // 隐藏统一悬浮提示
    hideUnifiedTooltip() {
      this.unifiedTooltip.visible = false;
      this.unifiedTooltip.currentBlockId = null;
    },

    // 检查是否有物料块
    hasMaterialBlock(block) {
      return block.materialName &&
        block.materialWeight &&
        block.materialWeight > 0;
    },



    // 全局鼠标按下处理
    handleGlobalMouseDown(event) {
      if (this.unifiedTooltip.visible) {
        const tooltipElement = document.querySelector('.unified-tooltip');
        if (tooltipElement && !tooltipElement.contains(event.target)) {
          this.hideUnifiedTooltip();
        }
      }
    },

    // 全局触摸开始处理
    handleGlobalTouchStart(event) {

      if (this.unifiedTooltip.visible) {
        const tooltipElement = document.querySelector('.unified-tooltip');
        if (tooltipElement && !tooltipElement.contains(event.target)) {

          this.hideUnifiedTooltip();
        }
      }
    },

    // 全局鼠标释放处理
    handleGlobalMouseUp(event) {

      if (this.isDragging) {
        this.isDragging = false;

      }
    },

    // 全局触摸结束处理
    handleGlobalTouchEnd(event) {

      if (this.isDragging) {
        this.isDragging = false;

      }
    },

    // 触摸事件处理
    handleTouchEnd(event, block) {

      if (this.touchStartTime && Date.now() - this.touchStartTime > 500) {
        const touch = event.changedTouches[0];
        this.showContextMenu({
          preventDefault: () => { },
          clientX: touch.clientX,
          clientY: touch.clientY
        }, block);
      }
      this.touchStartTime = null;
    },

    // 获取事件的客户端坐标
    getEventClientX(event) {
      return event.touches ? event.touches[0].clientX : event.clientX;
    },

    getEventClientY(event) {
      return event.touches ? event.touches[0].clientY : event.clientY;
    },
    // 右键菜单
    showContextMenu(event, block) {
      if (!this.isEditing) return;
      event.preventDefault();

      // 隐藏悬浮提示
      this.hideUnifiedTooltip();

      this.contextMenuVisible = true;
      this.contextMenuX = event.clientX;
      this.contextMenuY = event.clientY;
      this.currentContextBlock = block;
    },

    // 隐藏右键菜单
    hideContextMenu() {
      this.contextMenuVisible = false;
      this.currentContextBlock = null;
    },

    // 处理菜单项点击
    handleMenuClick(action) {
      if (!this.currentContextBlock) {
        this.$message.warning('请选择要操作的物料块');
        return;
      }

      switch (action) {
        case 'in':
          this.handleStockIn();
          break;
        case 'out':
          this.handleStockOut();
          break;
        case 'changeStack':
          this.handleChangeStack();
          break;
        case 'delete':
          this.handleDelete();
          break;
        default:
          console.warn('未知的菜单操作:', action);
      }
      this.hideContextMenu();
    },

    // 处理入库操作
    handleStockIn() {

      if (!this.currentContextBlock.materialName) {
        this.$message.warning('该位置没有物料，无法进行入库操作');
        return;
      }

      // 准备物料信息
      this.currentStockMaterial = {
        stockRealId: this.currentContextBlock.stockRealId, // 实际库存ID，可能为null
        stackPlanId: this.currentContextBlock.stackPlanId, // 计划ID，可能为null
        materialName: this.currentContextBlock.materialName || '未知物料',
        materialWeight: this.currentContextBlock.materialWeight || 0,
        position: this.currentContextBlock.position || '未知位置',

        ...this.currentContextBlock
      };

      this.stockOperationType = 'in';
      this.stockDialogVisible = true;
    },

    // 处理出库操作
    handleStockOut() {

      if (!this.currentContextBlock.materialName) {
        this.$message.warning('该位置没有物料，无法进行出库操作');
        return;
      }

      // 准备物料信息
      this.currentStockMaterial = {
        stockRealId: this.currentContextBlock.stockRealId, // 实际库存ID，可能为null
        stackPlanId: this.currentContextBlock.stackPlanId, // 计划ID，可能为null
        materialName: this.currentContextBlock.materialName ,
        materialWeight: this.currentContextBlock.materialWeight ,
        position: this.currentContextBlock.position,
        // 添加更多需要的字段
        ...this.currentContextBlock
      };

      this.stockOperationType = 'out';
      this.stockDialogVisible = true;
    },

    // 处理盘点操作
    handleChangeStack() {
      // 检查是否有物料（盘点可以对有物料或无物料的位置进行）
      if (!this.currentContextBlock) {
        this.$message.warning('请选择要盘点的位置');
        return;
      }

      // 准备物料信息
      this.currentInventoryMaterial = {
        stockRealId: this.currentContextBlock.stockRealId, // 实际库存ID，可能为null
        stackPlanId: this.currentContextBlock.stackPlanId, // 计划ID，可能为null
        materialName: this.currentContextBlock.materialName || '无物料',
        materialWeight: this.currentContextBlock.materialWeight || 0,
        position: this.currentContextBlock.position || '未知位置',
        // 添加更多需要的字段
        ...this.currentContextBlock
      };

      this.inventoryDialogVisible = true;
    },

    // 处理删除操作
    async handleDelete() {


      // 检查当前选中的块是否存在
      if (!this.currentContextBlock) {
        this.$message.warning('请选择要删除的物料块');
        return;
      }

      // 检查是否有物料数据
      if (!this.currentContextBlock.materialName) {
        this.$message.warning('该位置没有物料，无法进行删除操作');
        return;
      }

      // 检查重量是否为0
      const currentWeight = parseFloat(this.currentContextBlock.materialWeight) || 0;
      if (currentWeight !== 0) {
        this.$message.error('只有重量为0的物料才能删除，当前重量：' + currentWeight + 'T');
        return;
      }

      // 检查ID参数
      const stackPlanId = this.currentContextBlock.stackPlanId ||
        (this.currentContextBlock.planData && this.currentContextBlock.planData.stackPlanId) ||
        null;

      const stockRealId = this.currentContextBlock.stockRealId ||
        (this.currentContextBlock.realData && this.currentContextBlock.realData.stockRealId) ||
        null;

      console.log('🔍 删除参数检查:', {
        'stackPlanId': stackPlanId,
        'stockRealId': stockRealId,
        'currentContextBlock': this.currentContextBlock
      });

      // 检查是否有有效的ID
      if (!stackPlanId && !stockRealId) {
        this.$message.error('无法获取有效的计划ID或库存ID，无法执行删除操作');
        return;
      }

      // 确认删除操作
      try {
        await this.$confirm('确定要删除该物料记录吗？此操作不可恢复！', '删除确认', {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          type: 'warning',
          confirmButtonClass: 'el-button--danger'
        });

        // 准备删除参数
        const deleteData = {
          stackPlanId: stackPlanId, // 计划ID，可能为null
          stockRealId: stockRealId, // 库存ID，可能为null
          remark: '删除' // 操作标识
        };

        console.log('删除参数:', deleteData);

        // 调用删除API
        const response = await deleteStock(deleteData);

        if (response && response.code === 200) {
          this.$message.success('删除操作成功');

          this.refreshData();


          this.$emit('delete-success', {
            data: deleteData,
            response: response,
            block: this.currentContextBlock
          });
        } else {
          throw new Error(response.msg || '删除操作失败');
        }

      } catch (error) {
        if (error === 'cancel') {
          // 用户取消删除
          console.log('用户取消删除操作');
        } else {
          console.error('删除操作失败:', error);
          this.$message.error('删除操作失败: ' + (error.message || '未知错误'));
        }
      }
    },

    // 处理出库入库操作成功
    handleStockOperationSuccess(result) {


      const { type, data, response } = result;
      const operationText = type === 'in' ? '入库' : '出库';

      this.$message.success(`${operationText}操作成功，重量：${data.weight}T`);

      this.refreshData();

      this.$emit('stock-operation-success', result);
    },

    handleStockDialogClose() {
      this.currentStockMaterial = {};
      this.stockOperationType = 'in';
    },

    // 处理盘点操作成功
    handleInventorySuccess(result) {
      const { type, data, response, userRemark } = result;

      this.$message.success(`盘点操作成功，重量：${data.weight}T`);

      // 刷新数据
      this.refreshData();

      // 触发盘点成功事件
      this.$emit('inventory-success', result);
    },

    // 处理盘点弹窗关闭
    handleInventoryDialogClose() {
      this.currentInventoryMaterial = {};
    },


    async saveData() {
      // 组装 planList/realList
      const planList = [];
      const realList = [];
      this.warehouseRows.forEach(row => {
        row.blocks.forEach(block => {
          // 更新 planData 的位置信息
          if (block.planData) {
            // 计算计划块的刻度位置
            const planStartScale = this.maxScale - (block.planLeft / this.pixelScaleRatio);
            const planEndScale = planStartScale - (block.planWidth / this.pixelScaleRatio);

            // 更新planData的位置信息
            block.planData.startPosition = Math.round(planEndScale);   // 小值
            block.planData.endPosition = Math.round(planStartScale);   // 大值


            planList.push({ ...block.planData });
          }
          // 更新 realData 的位置信息
          if (block.realData) {
            if (block.position && block.position.includes('-')) {
              const parts = block.position.split('-').map(Number);

              block.realData.startPosition = parts[1];  // startPosition（小值）
              block.realData.endPosition = parts[0];    // endPosition（大值）


            }
            realList.push({ ...block.realData });
          }
        });
      });
      // 调用API
      try {
        const res = await updateStorehouseMapData({ planList, realList });
        if (res && res.code === 200) {
          this.$message.success('保存成功');
        } else {
          this.$message.error('保存失败: ' + (res.msg || '未知错误'));
        }
      } catch (e) {
        this.$message.error('保存失败: ' + e.message);
      }
    },
    // 切换编辑状态
    toggleEdit() {
      this.isEditing = !this.isEditing;

      if (this.isEditing) {
        // 进入编辑模式：保存当前大机显示状态并隐藏大机
        this.showStackersBeforeEdit = this.showStackers;
        if (this.showStackers) {
          this.showStackers = false;
          this.disconnectWebSocket();
          console.log('进入编辑模式，隐藏大机并断开WebSocket连接');
        }
      } else {
        // 退出编辑模式：恢复编辑前的大机显示状态
        if (this.showStackersBeforeEdit && !this.showStackers) {
          this.showStackers = true;
          this.initWebSocket(1);
          console.log('退出编辑模式，恢复大机显示并重新连接WebSocket');
        }
        this.hideUnifiedTooltip();
        this.hideContextMenu();
      }
    },









    // 物料块名称显示优化方法
    getDisplayName(materialName, blockWidth) {
      if (!materialName) return '';

      // 根据块宽度动态调整显示内容
      const charWidth = 12; // 每个字符宽度
      const availableWidth = blockWidth - 16; // 减去padding
      const maxChars = Math.floor(availableWidth / charWidth);

      if (materialName.length <= maxChars) {
        return materialName;
      }

      return this.getSmartAbbreviation(materialName, maxChars);
    },

    getSmartAbbreviation(name, maxChars) {
      if (maxChars <= 3) {
        return name.substring(0, maxChars - 1) + '…';
      }

      const abbreviations = {
        '澳大利亚': '澳',
        '巴西': '巴',
        '印度': '印',
        '南非': '南',
        '纽曼': '纽',
        '块矿': '块',
        '球团': '球',
        '精粉': '粉',
        '混匀': '混',
        '下粉': '粉',
        '外购': '外',
        '焦丁': '丁',
        '焦粉': '焦'
      };

      let abbreviated = name;
      for (const [full, short] of Object.entries(abbreviations)) {
        abbreviated = abbreviated.replace(new RegExp(full, 'g'), short);
      }

      if (abbreviated.length <= maxChars) {
        return abbreviated;
      }


      return abbreviated.substring(0, maxChars - 1) + '…';
    },

    getBlockTitleClass(block) {
      const width = block.materialWidth || 100;

      if (width < 60) {
        return 'title-small';
      } else if (width < 100) {
        return 'title-medium';
      } else if (width < 150) {
        return 'title-normal';
      } else {
        return 'title-large';
      }
    },

    // 物料块悬停处理
    handleMaterialBlockHover(event, block) {

      this.handleBlockHover(block.materialName);

      this.showMaterialTooltip(event, block);
    },

    // 物料块离开处理
    handleMaterialBlockLeave() {

      this.handleBlockLeave();


      this.hideMaterialTooltip();
    },

    showMaterialTooltip(event, block) {
      const rect = event.target.getBoundingClientRect();

      this.materialTooltip = {
        visible: true,
        x: rect.left + rect.width / 2,
        y: rect.top - 10,
        name: block.materialName || '未知物料',
        weight: block.materialWeight || 0,
        position: block.position || '-'
      };
    },

    hideMaterialTooltip() {
      this.materialTooltip.visible = false;
    },

    // ==================== 大机任务面板相关方法 ====================

    // 开始执行任务（未执行 → 正在执行）
    async startTask(task) {
      if (!task) {
        this.$message.warning('没有可执行的任务');
        return;
      }

      try {
        task.confirming = true;

        // 调用API开始执行任务，status=1表示开始执行
        const response = await updateStackerTaskStatus({
          id: task.id,
          status: "1"
        });

        if (response && response.code === 200) {
          this.$message.success('任务开始执行');
          // 重新加载任务列表
          await this.loadTaskList();
          // 重启定时器，确保数据同步
          this.startTaskListTimer();
        } else {
          this.$message.error('开始执行失败，请重试');
        }

      } catch (error) {
        console.error('开始执行任务失败:', error);
        this.$message.error('开始执行失败: ' + (error.message || '网络错误'));
      } finally {
        task.confirming = false;
      }
    },

    // 完成任务（正在执行 → 已完成）
    async completeTask(task) {
      if (!task) {
        this.$message.warning('没有可完成的任务');
        return;
      }

      try {
        task.confirming = true;

        // 调用API完成任务，status=2表示完成任务
        const response = await updateStackerTaskStatus({
          id: task.id,
          status: "2"
        });

        if (response && response.code === 200) {
          this.$message.success('任务完成');
          // 重新加载任务列表
          await this.loadTaskList();
          // 重启定时器，确保数据同步
          this.startTaskListTimer();
        } else {
          this.$message.error('完成任务失败，请重试');
        }

      } catch (error) {
        console.error('完成任务失败:', error);
        this.$message.error('完成任务失败: ' + (error.message || '网络错误'));
      } finally {
        task.confirming = false;
      }
    },

    // 加载大机任务列表
    async loadTaskList() {
      try {
        // 根据料场ID调用对应的接口
        let response;
        if (this.stockyardId === 'stockyard1') {
          response = await getStackerTasks_1();
        } else if (this.stockyardId === 'stockyard2') {
          response = await getStackerTasks_2();
        } else {
          console.error('未知的料场ID:', this.stockyardId);
          this.taskList = [];
          return;
        }

        if (response && response.code === 200 && response.data && response.data.length > 0) {
          // 保存所有任务，并为每个任务添加confirming状态
          this.taskList = response.data.map(task => ({
            ...task,
            confirming: false
          }));
        } else {
          this.taskList = [];
        }
      } catch (error) {
        console.error('加载大机任务列表失败:', error);
        this.taskList = [];
        this.$message.error('获取大机任务失败: ' + (error.message || '网络错误'));
      }
    },




  },
  computed: {
    // 判断当前用户角色（是否显示大机任务面板）
    isStackerOperator() {
      try {
        const roles = this.$store.getters.roles || [];
        console.log('当前用户角色:', roles);

        // 检查角色数组中是否包含tgylkgl角色
        const result = roles.includes('tgylkgl');

        console.log('是否为tgylkgl角色:', result);
        return result;
      } catch (error) {
        console.error('判断用户角色时出错:', error);
        return false;
      }
    },

    stripStyle() {
      return {
        width: `${this.maxScale * this.pixelScaleRatio}px`
      }
    },
    materialList: {
      get() {
        const materials = [];
        this.warehouseRows.forEach(row => {
          row.blocks.forEach(block => {

            if (!block.materialName || block.materialName === '无物料') {
              return;
            }

            //使用固定的position值
            let startPosition, endPosition;

            if (block.position && block.position.includes('-')) {

              const parts = block.position.split('-');
              endPosition = parseInt(parts[0]);
              startPosition = parseInt(parts[1]);

              if (isNaN(endPosition) || isNaN(startPosition)) {
                endPosition = 0;
                startPosition = 0;
              }
            } else {
              if (!this.pixelScaleRatio || this.pixelScaleRatio === 0) {
                startPosition = 0;
                endPosition = 0;
              } else {
                const materialTotalLeft = (block.planLeft || 0) + (block.materialLeft || 0);
                const materialWidth = block.materialWidth || 0;

                const materialScaleStart = this.maxScale - (materialTotalLeft / this.pixelScaleRatio);
                const materialScaleEnd = materialScaleStart - (materialWidth / this.pixelScaleRatio);

                startPosition = Math.round(materialScaleStart);
                endPosition = Math.round(materialScaleEnd);

                if (isNaN(startPosition) || isNaN(endPosition)) {
                  startPosition = 0;
                  endPosition = 0;
                }

                // 保持与解析逻辑一致的格式
                block.position = `${endPosition}-${startPosition}`;
              }
            }

            const finalStartPosition = isNaN(startPosition) ? 0 : startPosition;
            const finalEndPosition = isNaN(endPosition) ? 0 : endPosition;
            const finalWeight = isNaN(block.materialWeight) ? 0 : (block.materialWeight || 0);

            materials.push({
              id: block.id,
              name: block.materialName,
              startPosition: finalStartPosition,
              endPosition: finalEndPosition,
              weight: finalWeight
            });
          });
        });
        return materials;
      },
      set(newValue) {
        // materialList setter
      }
    },

    // 计算属性：过滤后的任务列表（不显示已完成的任务）
    filteredTaskList() {
      return this.taskList.filter(task => task.workStatus !== '已完成');
    }
  }
}
</script>

<style lang="scss" scoped>
.title-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 30px 10px 30px;

  backdrop-filter: blur(10px);
  border-bottom: 2px solid #2196f3;
  box-shadow: 0 4px 20px rgba(33, 150, 243, 0.1);
  position: relative;


  &.mobile {
    padding: 8px 15px;
    flex-wrap: wrap;
    gap: 10px;
  }


  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    z-index: 1;
  }


  .main-title,
  .header-buttons {
    position: relative;
    z-index: 2;
  }

  .main-title {
    font-size: 24px;
    font-weight: 600;
    color: #1976d2;
    margin: 0;
    text-shadow: 0 2px 4px rgba(25, 118, 210, 0.1);
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }

  .header-buttons {
    display: flex;
    gap: 15px;
    //margin-left: auto;

    .header-btn {
      padding: 8px 20px;
      background: linear-gradient(135deg, #42a5f5, #1e88e5);
      color: white;
      border: none;
      border-radius: 20px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 3px 10px rgba(30, 136, 229, 0.3);

      // 移动端适配
      @media (max-width: 768px) {
        padding: 6px 12px;
        font-size: 12px;
        min-height: 36px;
        touch-action: manipulation;
      }

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(30, 136, 229, 0.4);
        background: linear-gradient(135deg, #1e88e5, #1565c0);
      }

      &:active {
        transform: translateY(0);
      }

      &.toggle-btn {
        background: linear-gradient(135deg, #ff9800, #f57c00);

        &:hover {
          background: linear-gradient(135deg, #f57c00, #ef6c00);
        }
      }

      &.stacker-toggle-btn {
        // 显示大机时的样式（绿色）
        &.el-button--success {
          background: linear-gradient(135deg, #4caf50, #388e3c);
          box-shadow: 0 3px 10px rgba(76, 175, 80, 0.3);

          &:hover {
            background: linear-gradient(135deg, #388e3c, #2e7d32);
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
          }
        }

        // 隐藏大机时的样式（灰色）
        &.el-button--info {
          background: linear-gradient(135deg, #9e9e9e, #757575);
          box-shadow: 0 3px 10px rgba(158, 158, 158, 0.3);

          &:hover {
            background: linear-gradient(135deg, #757575, #616161);
            box-shadow: 0 6px 20px rgba(158, 158, 158, 0.4);
          }
        }
      }

      &.edit-btn {
        &.active {
          background: linear-gradient(135deg, #4caf50, #388e3c);
          box-shadow: 0 3px 10px rgba(76, 175, 80, 0.4);

          &:hover {
            background: linear-gradient(135deg, #388e3c, #2e7d32);
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.5);
          }
        }
      }


    }
  }
}

.area-map {


  .content-wrapper {
    display: flex;
    gap: 20px;
    background-color: #f7f8fe;
    transition: margin-right 0.3s ease;

    // 当面板打开时，为右侧面板预留空间
    &.panel-open {
      margin-right: 320px;

      &.tablet {
        margin-right: 280px;
      }

      &.mobile {
        margin-right: 0; // 移动端面板全屏覆盖，不需要预留空间
      }
    }

    // 移动端适配
    &.mobile {
      flex-direction: column;
      gap: 10px;
      padding: 0 10px;
    }

    &.tablet {
      gap: 15px;
      padding: 0 15px;
    }

    .map-container {
      flex: 1;
      transition: all 0.3s ease;

      // 当面板打开时，确保地图容器能够正确调整大小
      &.panel-open {
        // 触发重新计算容器尺寸
        .warehouse-container {
          transition: all 0.3s ease;
        }
      }

      // 移动端适配
      @media (max-width: 768px) {
        min-height: 60vh;
        overflow-x: auto;
      }

      .title-bar {
        margin-bottom: 20px;

        h3 {
          font-size: 18px;
          color: #303133;
          margin: 0;
        }
      }

      .warehouse-container {
        .warehouse-row {
          display: flex;
          margin-bottom: 30px;
          position: relative;

          .row-label {
            width: 30px;
            height: 30px;
            background: #409EFF;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            margin-right: 10px;
            font-weight: bold;
            align-self: center;
            margin-top: 30px;

            // 移动端适配
            @media (max-width: 768px) {
              width: 25px;
              height: 25px;
              font-size: 12px;
              margin-right: 8px;
              margin-top: 25px;
            }
          }

          .ruler-container {
            flex: 1;
            position: relative;
            padding-top: 20px;

            .ruler {
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              height: 20px;
              display: flex;
              justify-content: space-between;
              align-items: flex-end;
              border-top: 1px solid #DCDFE6;
              background-color: #f7f8fa;

              .scale-mark {
                display: flex;
                flex-direction: column;
                align-items: center;
                position: relative;
                padding: 0 2px;

                &.no-number {
                  visibility: hidden;
                }

                .scale-line {
                  width: 1px;
                  height: 6px;
                  background-color: #DCDFE6;
                  margin-bottom: -1px;
                }

                .scale-number {
                  font-size: 12px;
                  color: #606266;
                  margin-bottom: 4px;

                  // 移动端适配
                  @media (max-width: 768px) {
                    font-size: 10px;
                  }
                }
              }
            }

            .material-strip {
              position: relative;
              height: 100px;
              background: #fff;
              //margin-top: 10px;
              // width 通过 stripStyle 计算绑定

              // 移动端适配
              @media (max-width: 768px) {
                height: 80px;
                margin-top: 8px;
              }


              .block-container {
                background: rgba(64, 158, 255, 0.1);
                border: none;
                border-radius: 4px;
                position: absolute;
                height: 80px;
                top: 10px;
                z-index: 2;
                cursor: default;
                user-select: none;
                transition: all 0.3s ease;


                &.editable {
                  cursor: move;

                  &:hover {
                    background: rgba(64, 158, 255, 0.2);
                    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
                    transform: translateY(-1px);
                  }
                }


                &.readonly {
                  cursor: default;

                  &:hover {
                    background: rgba(64, 158, 255, 0.15);
                    transform: none;
                  }
                }


                &.highlighted {
                  background: rgba(245, 108, 108, 0.2);
                  box-shadow: 0 0 10px rgba(245, 108, 108, 0.3);
                }
              }

              .plan-block {
                position: absolute;
                height: 40px;
                top: 0;
                left: 0;
                right: 0;
                background: transparent;
                border: none;
                border-radius: 4px;
                cursor: inherit;
                user-select: none;
                transition: all 0.3s ease;





                .plan-content {
                  width: 100%;
                  height: 100%;
                  display: flex;
                  align-items: flex-start;
                  padding: 2px 4px 0 4px;
                  overflow: hidden;

                  .plan-title {
                    font-size: 11px;
                    color: #606266;
                    font-weight: 500;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    max-width: 100%;
                    line-height: 1.2;
                    flex: 1;
                    min-width: 0;
                  }
                }

                .resize-handles {
                  .handle {
                    position: absolute;
                    top: 0;
                    bottom: 0;
                    width: 6px;
                    cursor: col-resize;
                    z-index: 1;


                    @media (max-width: 768px) {
                      width: 12px;
                    }

                    &.left {
                      left: 0;
                    }

                    &.right {
                      right: 0;
                    }

                    &:hover {
                      background: rgba(64, 158, 255, 0.2);
                    }
                  }
                }
              }

              .material-block {
                position: absolute;
                height: 60px;
                top: 20px;
                background: #409EFF;
                border-radius: 4px;
                box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
                cursor: default; // 默认为普通指针
                touch-action: none; // 禁用默认触摸行为
                transition: all 0.3s ease;

                // 编辑模式下的样式
                &.editable {
                  cursor: move;
                }

                // 只读模式下的样式
                &.readonly {
                  cursor: default;
                }

                // 编辑模式样式
                &.editable {
                  cursor: move;
                  border: 2px solid transparent;

                  &:hover {
                    border-color: #67c23a;
                    box-shadow: 0 4px 12px rgba(103, 194, 58, 0.3);
                    transform: translateY(-1px);
                  }
                }

                // 只读模式样式
                &.readonly {
                  cursor: default;
                  opacity: 0.9;

                  &:hover {
                    transform: none;
                    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
                  }
                }

                // 负重量样式（红色）
                &.negative-weight {
                  background: #F56C6C !important;
                  box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3) !important;

                  &:hover {
                    box-shadow: 0 4px 12px rgba(245, 108, 108, 0.4) !important;
                  }

                  &.editable:hover {
                    border-color: #F56C6C !important;
                    box-shadow: 0 4px 12px rgba(245, 108, 108, 0.4) !important;
                  }
                }

                // 移动端适配
                @media (max-width: 768px) {
                  height: 50px;
                  top: 15px;
                }

                .block-content {
                  height: 100%;
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  justify-content: center;
                  color: white;

                  .block-title {
                    font-size: 16px; // 调整默认字体大小
                    margin-bottom: 4px;
                    line-height: 1.2;
                    text-align: center;
                    color: white;
                    font-weight: 500;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    word-break: break-word;

                    &.title-small {
                      font-size: 13px; // 调整小字体
                      line-height: 1.1;
                      white-space: nowrap;
                      overflow: hidden;
                      text-overflow: ellipsis;
                      max-width: 100%;
                    }

                    &.title-medium {
                      font-size: 14px; // 调整中等字体
                      line-height: 1.1;
                      max-height: 22px;
                      overflow: hidden;
                      display: -webkit-box;
                      -webkit-line-clamp: 2;
                      -webkit-box-orient: vertical;
                      text-overflow: ellipsis;
                      word-break: break-word;
                    }

                    &.title-normal {
                      font-size: 15px; // 调整正常字体
                      line-height: 1.2;
                      max-height: 26px;
                      overflow: hidden;
                      display: -webkit-box;
                      -webkit-line-clamp: 2;
                      -webkit-box-orient: vertical;
                      text-overflow: ellipsis;
                      word-break: break-word;
                    }

                    &.title-large {
                      font-size: 16px; // 调整大字体到16px
                      line-height: 1.3;
                      max-height: 32px;
                      overflow: hidden;
                      display: -webkit-box;
                      -webkit-line-clamp: 2;
                      -webkit-box-orient: vertical;
                      text-overflow: ellipsis;
                      word-break: break-word;
                    }
                  }

                  .block-weight {
                    font-size: 11px; // 减小重量字体大小
                    font-weight: bold;
                    color: white;
                    text-align: center;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    max-width: 100%;
                  }
                }

                .resize-handles {
                  .handle {
                    position: absolute;
                    top: 0;
                    bottom: 0;
                    width: 6px;
                    cursor: col-resize;
                    z-index: 2;

                    &.left {
                      left: 0;
                    }

                    &.right {
                      right: 0;
                    }

                    &:hover {
                      background: rgba(255, 255, 255, 0.2);
                    }
                  }
                }
              }
            }
          }
        }
      }
    }

    .material-info {
      z-index: 99; //物料列表z-index在主页面上层，避免窗口缩小时 物料块覆盖无聊列表
      width: 300px;
      border: 1px solid #DCDFE6;
      border-radius: 4px;
      padding: 5px;
      background: #fff;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

      // 移动端适配
      &.mobile {
        width: 100%;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 1000;
        border-radius: 0;
        padding: 20px;
        max-height: 100vh;
        overflow-y: auto;
      }

      &.tablet {
        width: 280px;
      }

      .info-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        padding-bottom: 8px;
        border-bottom: 1px solid #EBEEF5;

        h3 {
          margin: 0;
          font-size: 16px;
          color: #303133;
        }

        .debug-buttons {
          display: flex;
          gap: 4px;

          .el-button {
            padding: 4px 8px;
            font-size: 11px;
          }
        }

        .close-btn {
          color: #909399;
          font-size: 16px;

          &:hover {
            color: #409EFF;
          }
        }
      }

      .info-list {
        .info-item-header {
          display: flex;
          padding: 8px 0;
          font-weight: bold;
          color: #606266;
          border-bottom: 2px solid #EBEEF5;

          .col-name {
            flex: 1;
          }

          .col-position {
            flex: 1;
            text-align: center;
          }

          .col-weight {
            flex: 1;
            text-align: right;
            padding-right: 10px
          }
        }

        .info-item-body {
          height: calc(100vh - 200px);
          overflow-y: auto;

          &::-webkit-scrollbar {
            width: 6px;
          }

          &::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
          }

          &::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;

            &:hover {
              background: #a8a8a8;
            }
          }
        }

        .info-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 5px 0;
          border-bottom: 1px solid #EBEEF5;
          transition: all 0.3s ease;
          cursor: pointer;

          &:last-child {
            border-bottom: none;
          }

          &.highlighted {
            background: rgba(64, 158, 255, 0.1);
            transform: translateY(-5px);
          }

          &:hover {
            background: rgba(64, 158, 255, 0.05);
          }

          .material-name {
            flex: 1;
            color: #303133;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }

          .data-position {
            flex: 1;
            color: #409EFF;
            font-size: 14px;
            text-align: center;
          }

          .data-weight {
            flex: 1;
            color: #000000;
            font-size: 14px;
            text-align: right;
            padding-right: 5px;
          }
        }
      }
    }
  }
}

.area-map {
  padding: 0px 20px 0 20px;
  background: #fff;

  // 移动端适配
  @media (max-width: 768px) {
    padding: 0;
    overflow-x: hidden;
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .title {
      font-size: 18px;
      font-weight: bold;
    }
  }

  .content {
    .scale-ruler {
      height: 30px;
      border-bottom: 1px solid #DCDFE6;
      position: relative;

      .scale-marks {
        display: flex;
        justify-content: space-between;
        padding: 0 10px;

        .mark {
          font-size: 12px;
          color: #909399;
        }
      }
    }

    .strips-container {
      .material-strip {
        display: flex;
        align-items: center;

        .strip-label {
          width: 30px;
          height: 30px;
          background: #409EFF;
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 4px;
          margin-right: 10px;
        }

        .strip-content {
          flex: 1;
          position: relative;
          height: 100%;
        }
      }
    }
  }
}

.plan-block {
  &.highlighted {
    //background: rgba(64, 158, 255, 0.2);

    .material-block {
      box-shadow: 0 0 10px rgba(64, 158, 255, 0.5);
    }
  }
}

.material-block {
  &.highlighted {
    background: #1890ff;
    box-shadow: 0 0 10px rgba(64, 158, 255, 0.5);
  }
}

.info-item {
  cursor: pointer;
  transition: all 0.3s;

  &.highlighted {
    background: rgba(64, 158, 255, 0.1);
    transform: translateX(-5px);
  }

  &:hover {
    background: rgba(64, 158, 255, 0.05);
  }
}

.context-menu {
  position: fixed;
  background: url("~@/assets/images/imageKQT/messageL.png") 58% 100%/128% 100% no-repeat;
  z-index: 9999;
  padding: 10px 0 6px 0;
  font-family: Arial, Helvetica, sans-serif;

  .menu-item {
    margin: 3px 3px 0px 16px;
    font-size: 14px;
    padding: 2px 16px 2px 2px;
    color: #1c63af;
    cursor: pointer;
    transition: background-color 0.3s;
    background-color: #ddeafd;
    display: flex;
    align-items: center;

    i {
      margin-right: 6px;
      font-size: 14px;
      width: 14px;
      text-align: center;
    }

    &:hover {
      background-color: #488ff7;
      color: #fff;

      i {
        color: #fff;
      }
    }
  }
}

// 移动端强制横屏样式
@media (max-width: 768px) {
  // 强制横屏显示 - 方案1: 竖屏时旋转整个页面
  @media (orientation: portrait) {

    html,
    body {
      width: 100vh;
      height: 100vw;
      transform: rotate(90deg);
      transform-origin: 50% 50%;
      overflow: hidden;
      position: fixed;
      top: 0;
      left: 0;
    }

    .area-map {
      width: 100vh;
      height: 100vw;
      transform: none;
      position: relative;
    }

    // 备用方案：如果旋转效果不好，则显示提示
    .orientation-tip {
      display: flex !important;
      opacity: 1 !important;
      visibility: visible !important;
      z-index: 10000;
    }
  }

  // 横屏时的优化
  @media (orientation: landscape) {

    html,
    body {
      transform: none;
      width: 100vw;
      height: 100vh;
      position: relative;
      overflow: auto;
    }

    .orientation-tip {
      display: none !important;
    }

    .area-map {
      width: 100vw;
      height: 100vh;
      overflow: hidden;

      .content-wrapper {
        height: calc(100vh - 60px);

        &.mobile {
          flex-direction: row;

          .map-container {
            flex: 1;
            height: 100%;
            overflow: auto;
          }

          .material-info {
            &.mobile {
              position: relative;
              width: 300px;
              height: 100%;
              overflow-y: auto;
            }
          }
        }
      }
    }
  }

  // 全局触摸优化
  * {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  // 防止页面缩放
  html {
    touch-action: manipulation;
  }

  // 优化滚动
  .warehouse-container {
    -webkit-overflow-scrolling: touch;
  }

  // 增大可点击区域
  .el-button {
    min-height: 44px;
    min-width: 44px;
  }

  // 物料块触摸优化
  .material-block,
  .plan-block {
    min-width: 44px;
    min-height: 44px;
  }
}

// 平板适配
@media (min-width: 769px) and (max-width: 1024px) {
  .area-map {
    .content-wrapper {
      gap: 15px;
    }

    .material-info {
      width: 280px;
    }
  }
}

// 横屏提示样式
.orientation-tip {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;

  &.show {
    opacity: 1;
    visibility: visible;
  }

  .tip-content {
    text-align: center;
    color: white;
    padding: 40px 20px;
    max-width: 300px;

    .phone-icon {
      font-size: 60px;
      margin-bottom: 20px;
      animation: shake 2s infinite;
    }

    .tip-text {
      margin-bottom: 20px;

      h3 {
        font-size: 20px;
        margin: 0 0 10px 0;
        color: #fff;
      }

      p {
        font-size: 14px;
        margin: 0 0 8px 0;
        color: #ccc;
        line-height: 1.5;

        &.sub-tip {
          font-size: 12px;
          color: #999;
          margin-bottom: 0;
        }
      }
    }

    .rotate-icon {
      font-size: 40px;
      animation: rotate 2s linear infinite;
    }
  }
}

// 动画效果
@keyframes shake {

  0%,
  100% {
    transform: rotate(0deg);
  }

  25% {
    transform: rotate(-5deg);
  }

  75% {
    transform: rotate(5deg);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

// 移动端横屏优化
@media (max-width: 768px) and (orientation: landscape) {
  .orientation-tip {
    display: none !important;
  }

  .area-map {
    .content-wrapper {
      &.mobile {
        flex-direction: row;
        height: 100vh;
        overflow: hidden;

        .map-container {
          flex: 1;
          height: 100vh;
          overflow: auto;
        }

        .material-info {
          &.mobile {
            position: relative;
            width: 300px;
            height: 100vh;
            overflow-y: auto;
          }
        }
      }
    }
  }
}

// 强制横屏的全局样式
html.force-landscape,
body.force-landscape {
  @media (max-width: 768px) and (orientation: portrait) {
    width: 100vh !important;
    height: 100vw !important;
    transform: rotate(90deg) !important;
    transform-origin: 50% 50% !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    overflow: hidden !important;

    // 确保所有子元素也适应旋转
    * {
      box-sizing: border-box;
    }
  }
}

// 强制横屏时的特殊处理
@media (max-width: 768px) and (orientation: portrait) {
  html.force-landscape {
    .area-map {
      width: 100vh !important;
      height: 100vw !important;
      transform: none !important;
      position: relative !important;
      overflow: hidden !important;

      .content-wrapper {
        height: calc(100vw - 60px) !important;

        &.mobile {
          flex-direction: row !important;

          .map-container {
            flex: 1 !important;
            height: 100% !important;
            overflow: auto !important;
          }

          .material-info {
            &.mobile {
              position: relative !important;
              width: 300px !important;
              height: 100% !important;
              overflow-y: auto !important;
            }
          }
        }
      }
    }
  }
}

// 防止iOS Safari的地址栏影响
@supports (-webkit-touch-callout: none) {
  @media (max-width: 768px) and (orientation: portrait) {

    html.force-landscape,
    body.force-landscape {
      height: 100vh !important;
      height: -webkit-fill-available !important;
    }
  }
}

// 物料悬浮提示样式
.material-tooltip {
  position: fixed;
  z-index: 9999;
  pointer-events: none;
  transform: translateX(-50%) translateY(-100%);

  .tooltip-content {
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 12px 16px;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    font-size: 13px;
    line-height: 1.4;
    max-width: 250px;
    word-wrap: break-word;

    // 小箭头
    &::after {
      content: '';
      position: absolute;
      top: 100%;
      left: 50%;
      transform: translateX(-50%);
      border: 6px solid transparent;
      border-top-color: rgba(0, 0, 0, 0.9);
    }

    .tooltip-title {
      font-weight: bold;
      margin-bottom: 8px;
      color: #fff;
      border-bottom: 1px solid rgba(255, 255, 255, 0.2);
      padding-bottom: 6px;
    }

    .tooltip-info {
      .info-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 4px;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          color: #ccc;
          margin-right: 8px;
        }

        .value {
          color: #fff;
          font-weight: 500;
        }
      }
    }
  }
}

// 统一悬浮提示样式
.unified-tooltip {
  position: fixed;
  z-index: 9999;
  pointer-events: none;
  transform: translateX(-50%) translateY(-100%);

  .tooltip-content {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(30, 30, 30, 0.95));
    color: white;
    padding: 16px 20px;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    font-size: 13px;
    line-height: 1.4;
    max-width: 320px;
    word-wrap: break-word;
    border: 1px solid rgba(255, 255, 255, 0.1);

    // 小箭头
    &::after {
      content: '';
      position: absolute;
      top: 100%;
      left: 50%;
      transform: translateX(-50%);
      border: 8px solid transparent;
      border-top-color: rgba(0, 0, 0, 0.9);
    }

    // 计划部分
    .plan-section {
      .section-header {
        display: flex;
        align-items: center;
        margin-bottom: 8px;

        .plan-type {
          background: linear-gradient(135deg, #409EFF, #1890ff);
          color: white;
          font-size: 10px;
          padding: 3px 8px;
          border-radius: 4px;
          margin-right: 8px;
          font-weight: 500;
        }

        .section-title {
          font-weight: bold;
          color: #fff;
          flex: 1;
        }
      }

      .section-info {
        margin-left: 16px;

        .info-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 4px;

          .label {
            color: #ccc;
            margin-right: 8px;
          }

          .value {
            color: #409EFF;
            font-weight: 500;
          }
        }
      }
    }

    // 物料部分
    .material-section {
      .section-header {
        display: flex;
        align-items: center;
        margin-bottom: 8px;

        .material-type {
          background: linear-gradient(135deg, #67C23A, #52c41a);
          color: white;
          font-size: 10px;
          padding: 3px 8px;
          border-radius: 4px;
          margin-right: 8px;
          font-weight: 500;
        }

        .section-title {
          font-weight: bold;
          color: #fff;
          flex: 1;
        }
      }

      .section-info {
        margin-left: 16px;

        .info-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 4px;

          .label {
            color: #ccc;
            margin-right: 8px;
          }

          .value {
            color: #67C23A;
            font-weight: 500;
          }
        }
      }
    }

    // 分隔线
    .section-divider {
      height: 1px;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      margin: 12px 0;
    }



    // 仅计划时的状态
    .plan-only-section {
      margin-top: 12px;
      padding-top: 8px;
      border-top: 1px solid rgba(255, 255, 255, 0.1);

      .plan-status {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .status-label {
          color: #ccc;
          font-size: 12px;
        }

        .status-value {
          font-weight: bold;
          font-size: 12px;
          padding: 2px 8px;
          border-radius: 4px;

          &.empty {
            background: rgba(230, 162, 60, 0.2);
            color: #E6A23C;
          }
        }
      }
    }
  }
}

/* 大机独立移动层样式 */
.stacker-mobile-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  /* 不阻挡下层交互 */
  z-index: 500;
  /* 最高层级 */
}

.mobile-stacker {
  pointer-events: auto;
  /* 大机本身可交互 */
  transition: left 2s ease-in-out;
  /* 平滑移动动画 */
}

.mobile-stacker:hover {
  transform: translateX(-50%) scale(1.02);
  /* 悬停轻微放大 */
}

.mobile-stacker.moving {
  box-shadow: 0 0 20px rgba(255, 165, 0, 0.6);
  /* 移动时发光效果 */
}

/* 大机定位容器样式 */

/* 大机层容器 - 相对于刻度尺定位 */
.stacker-layer-for-row {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  /* 不阻挡下层交互 */
  z-index: 100;
}

/* 基础容器样式 */
.stacker-container {
  position: absolute;
  transform: translateX(-50%);
  /* 中心对准刻度线 */
  transition: all 0.3s ease;
  pointer-events: auto;
  /* 容器本身可交互 */
  overflow: visible;
  /* 允许悬浮窗溢出显示 */
  z-index: 100;
  /* 确保悬浮窗显示在合适的层级 */
}

/* 大机容器样式完成 - 让组件自己处理交互效果 */

/* WebSocket连接状态提示样式 */
.websocket-loading-global {
  position: absolute;
  top: 16px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 14px 28px;
  border-radius: 25px;
  box-shadow:
    0 8px 32px rgba(102, 126, 234, 0.3),
    0 2px 8px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  animation: slideInDown 0.4s ease-out;

  .loading-content {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 14px;
    font-weight: 600;
    letter-spacing: 0.5px;

    i {
      font-size: 18px;
      animation: rotate 1.2s linear infinite;
      color: #ffffff;
      filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
    }

    span {
      white-space: nowrap;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    }
  }

  /* 悬停效果 */
  &:hover {
    transform: translateX(-50%) translateY(-2px);
    box-shadow:
      0 12px 40px rgba(102, 126, 234, 0.4),
      0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

/* 动画定义 */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

/* 移动端适配 */
@media (max-width: 768px) {
  .websocket-loading-global {
    padding: 12px 20px;
    top: 12px;
    border-radius: 20px;

    .loading-content {
      font-size: 13px;
      gap: 10px;

      i {
        font-size: 16px;
      }
    }
  }
}

/* 平板适配 */
@media (max-width: 1024px) and (min-width: 769px) {
  .websocket-loading-global {
    padding: 13px 24px;
    top: 14px;

    .loading-content {
      font-size: 13px;

      i {
        font-size: 17px;
      }
    }
  }
}

/* ==================== 大机任务面板样式 ==================== */
.stacker-task-panel {
  width: 350px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;

  .info-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
    }

    .close-btn {
      color: white;
      padding: 0;

      &:hover {
        color: #f0f0f0;
      }
    }
  }

  .task-content {
    padding: 20px;

    .task-instruction {
      .instruction-text {
        background: #f8f9fa;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        font-size: 16px;
        line-height: 1.6;
        color: #2c3e50;
        text-align: center;
        min-height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .task-actions {
        text-align: center;

        .el-button {
          padding: 12px 30px;
          font-size: 16px;
          font-weight: 600;
        }
      }
    }

    .no-task {
      padding: 60px 20px;
      text-align: center;
      color: #95a5a6;

      i {
        font-size: 48px;
        margin-bottom: 16px;
        display: block;
        color: #bdc3c7;
      }

      span {
        font-size: 16px;
      }
    }
  }

  // 移动端适配
  &.mobile {
    width: 100%;
    position: fixed;
    top: 60px;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    border-radius: 0;

    .task-content {
      padding: 15px;
      height: calc(100vh - 120px);
      display: flex;
      flex-direction: column;
      justify-content: center;

      .task-instruction {
        .instruction-text {
          font-size: 14px;
          padding: 15px;
          min-height: 60px;
        }

        .task-actions .el-button {
          padding: 10px 25px;
          font-size: 14px;
        }
      }

      .no-task {
        padding: 40px 15px;

        i {
          font-size: 36px;
          margin-bottom: 12px;
        }

        span {
          font-size: 14px;
        }
      }
    }
  }

  // 平板适配
  &.tablet {
    width: 320px;

    .task-content {
      .task-instruction {
        .instruction-text {
          font-size: 15px;
          padding: 18px;
        }

        .task-actions .el-button {
          padding: 11px 28px;
          font-size: 15px;
        }
      }
    }
  }
}

/* ==================== 侧边面板样式 ==================== */
.side-panel {
  position: fixed;
  top: 80px;
  right: 0;
  width: 320px;
  height: calc(100vh - 80px);
  background: #fff;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  animation: slideInRight 0.3s ease-out;

  .panel-header {
    //background: linear-gradient(135deg, #667eea 100%);
    background: rgb(82, 167, 255);
    color: white;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;

    h3 {
      margin: 0;
      color: rgb(87 95 103);
      font-size: 16px;
      font-weight: 600;
    }

    .close-btn {
      color: white;
      padding: 0;

      &:hover {
        color: #f0f0f0;
      }
    }
  }

  .panel-task-content {
    padding: 20px;
    flex: 1;
    overflow-y: auto;

    .task-list {
      .task-item {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        margin-bottom: 15px;
        padding: 15px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

        &:last-child {
          margin-bottom: 0;
        }

        .task-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;

          .task-number {
            font-size: 14px;
            font-weight: 600;
            color: #666;
          }

          .status-value {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;

            &.status-pending {
              background: #fff3cd;
              color: #856404;
              border: 1px solid #ffeaa7;
            }

            &.status-executing {
              background: #d1ecf1;
              color: #0c5460;
              border: 1px solid #bee5eb;
            }

            &.status-completed {
              background: #d4edda;
              color: #155724;
              border: 1px solid #c3e6cb;
            }

            &.status-unknown {
              background: #f8f9fa;
              color: #6c757d;
              border: 1px solid #dee2e6;
            }
          }
        }

        .task-info {
          margin-bottom: 12px;

          .instruction-text {
            background: #fff;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 12px;
            font-size: 14px;
            line-height: 1.5;
            color: #2c3e50;
            min-height: 40px;
            display: flex;
            align-items: center;
          }
        }

        .task-actions {
          text-align: right;

          .el-button {
            padding: 8px 16px;
            font-size: 14px;
          }
        }
      }
    }

    .no-task {
      padding: 60px 20px;
      text-align: center;
      color: #95a5a6;

      i {
        font-size: 48px;
        margin-bottom: 16px;
        display: block;
        color: #bdc3c7;
      }

      span {
        font-size: 16px;
      }
    }
  }

  .panel-material-content {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .info-list {
      flex: 1;
      display: flex;
      flex-direction: column;

      .info-item-header {
        display: grid;
        grid-template-columns: 1fr 70px 70px;
        gap: 8px;
        padding: 15px 20px;
        background: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
        font-size: 14px;
        font-weight: 600;
        color: #666;
        flex-shrink: 0;

        span {
          text-align: center;

          &.col-name {
            text-align: left;
          }
        }
      }

      .info-item-body {
        flex: 1;
        overflow-y: auto;
        max-height: calc(100vh - 200px); // 确保有明确的最大高度

        // 自定义滚动条样式
        &::-webkit-scrollbar {
          width: 6px;
        }

        &::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 3px;

          &:hover {
            background: #a8a8a8;
          }
        }

        .info-item {
          display: grid;
          grid-template-columns: 1fr 70px 70px;
          gap: 8px;
          padding: 15px 20px;
          border-bottom: 1px solid #f0f0f0;
          transition: all 0.3s ease;
          align-items: center;

          &:hover {
            background: #f8f9fa;
          }

          &.highlighted {
            background: #e6f7ff;
            border-left: 4px solid #1890ff;
          }

          .material-name {
            font-size: 14px;
            color: #34495e;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .data-position {
            font-size: 13px;
            color: #7f8c8d;
            text-align: center;
          }

          .data-weight {
            font-size: 13px;
            color: #2c3e50;
            font-weight: 600;
            text-align: center;
          }
        }
      }
    }
  }

  // 移动端适配
  &.mobile {
    width: 100%;
    top: 0;
    height: 100vh;

    .panel-header {
      padding: 20px;
    }

    .panel-task-content {
      padding: 15px;

      .task-list {
        .task-item {
          padding: 12px;
          margin-bottom: 12px;

          .task-header {
            margin-bottom: 10px;

            .task-number {
              font-size: 13px;
            }

            .status-value {
              font-size: 11px;
              padding: 3px 6px;
            }
          }

          .task-info {
            margin-bottom: 10px;

            .instruction-text {
              font-size: 13px;
              padding: 10px;
              min-height: 35px;
            }
          }

          .task-actions .el-button {
            padding: 6px 12px;
            font-size: 13px;
          }
        }
      }

      .no-task {
        padding: 40px 15px;

        i {
          font-size: 36px;
          margin-bottom: 12px;
        }

        span {
          font-size: 14px;
        }
      }
    }

    .panel-material-content {
      .info-list {
        .info-item-body {
          max-height: calc(100vh - 160px); // 移动端调整最大高度
          -webkit-overflow-scrolling: touch; // iOS平滑滚动
        }
      }
    }
  }

  // 平板适配
  &.tablet {
    width: 280px;

    .panel-task-content {
      .task-list {
        .task-item {
          .task-header {
            .status-value {
              font-size: 12px;
            }
          }

          .task-info {
            .instruction-text {
              font-size: 14px;
              padding: 11px;
            }
          }

          .task-actions .el-button {
            padding: 7px 14px;
            font-size: 14px;
          }
        }
      }
    }
  }
}

// 滑入动画
@keyframes slideInRight {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}
</style>
